#include "BluetoothSerial.h"
#include "bluetooth_protocol.h"
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "DisplayDriver.h"
#include "LEDController.h"
#include "config.h"
#include "FontData.h"
#include "LittleFS.h"
#include "AnimatedGIF.h"
#include "file_handler.h"
#include "gif_player.h"
#include "debug_utils.h"
#include "app_list.h"
#include "clock_mode.h"
#include "score_mode.h"

int ea=0;
int eb=0;

BluetoothSerial SerialBT;
String device_name = BT_DEVICE_NAME;
BluetoothProtocolParser btParser;                       // 蓝牙协议解析器
BluetoothFrame currentFrame;                            // 当前解析的帧
loopstate currentloopstate = loopstate::loop_state_gif; // 🆕 初始化为分区显示模式（测试分区功能）

// 🆕 分区模式状态跟踪
uint8_t currentSplitMode = SPLIT_MODE_DISABLED; // 当前分区模式状态

bool isnext_list_name = true;                    // 是否切换下一个文件名
bool is_gif_exist_listname = true;               // 是否存在文件名
int current_list_mode = list_mode::LIST_NO_MODE; // 当前列表模式

// Check if Bluetooth is available
#if !defined(CONFIG_BT_ENABLED) || !defined(CONFIG_BLUEDROID_ENABLED)
#error Bluetooth is not enabled! Please run `make menuconfig` to and enable it
#endif

// Check Serial Port Profile
#if !defined(CONFIG_BT_SPP_ENABLED)
#error Serial Port Profile for Bluetooth is not available or not enabled. It is only available for the ESP32 chip.
#endif

// 函数声明
void handleParseResult(ParseResult result);
void processBluetoothCommand(const BluetoothFrame &frame);
void handleTextCommand(const BluetoothFrame &frame);
void handleUpperTextCommand(const uint16_t *fontData, int charCount); // 独立处理上半屏
void handleLowerTextCommand(const uint16_t *fontData, int charCount); // 独立处理下半屏
void handleColorCommand(const BluetoothFrame &frame);
void handleBorderCommand(const BluetoothFrame &frame);
void handleSpecificColorCommand(const BluetoothFrame &frame);
void handleRandomColorCommand(const BluetoothFrame &frame);
void handleSplitModeCommand(const BluetoothFrame &frame); // 🆕 处理分区模式命令
void handleClockModeCommand(const BluetoothFrame &frame); // 处理时钟命令
void handleScoreModeCommand(const BluetoothFrame &frame); // 处理计分命令


// 🔧 新增辅助函数：统一处理从GIF模式切换到文本模式
void switchFromGifToTextMode(const char *reason);
void setSplitDisplayLayout(uint8_t mode, const char *reason); // 🆕 设置分区显示布局

void handleAppListStartCommand(const BluetoothFrame &frame); // 处理列表开始命令
void handleAppListDataCommand(const BluetoothFrame &frame);  // 处理列表数据命令
void handleAppListEndCommand(const BluetoothFrame &frame);   // 处理列表结束命令
//  时钟和计分模式清屏辅助函数
void clearEntireScreen();
void exitAllModes();
void safeModeSwitchToClockMode();
void safeModeSwitchToScoreMode();


void setup()
{
    Serial.println("=== ESP32 LED SCREEN CONTROLLER ===");

    Serial.begin(SERIAL_BAUD_RATE);

    // 初始化显示硬件
    if (!initializeDisplay())
    {
        Serial.println("LED matrix initialization failed!");
        return;
    }

    // 启动蓝牙串口
    SerialBT.begin(device_name);
    Serial.println("bluetooth initialized successfully ");
    Serial.printf("Device name: %s\n", device_name.c_str());

    // 初始化LittleFS文件系统
    if (!initLittleFS())
    {
        DEBUG_ERROR("LittleFS initialization failed!");
        return;
    }

    // 创建gifs目录
    createGifsDirectory();

    // 显示内存信息
    printMemoryInfo();

    // 初始化列表
    initFileList();

    // 显示调试级别信息
    DebugController::printDebugStatus();

    // 根据字体大小设置初始点阵数据（使用FontData中的示例数据）
    if (currentFontSize == BT_FONT_32x32)
    {
        handleFullScreenTextCommand(full_text, getFullTextCharCount());
    }
    else
    {
        handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
    }

    // 初始化新增颜色系统
    initSpecificColorSystem();
    initRandomColorSystem();

    // 播放开机动画
    if (!playGIFAuto(GIF_STARTUP_FILE))
    {
        DEBUG_WARN("Unable to play startup animation");
        // 显示内存信息以帮助调试
        printMemoryInfo();
    }
}

void loop()
{
    while (SerialBT.available())
    {
        uint8_t receivedByte = SerialBT.read();
        ParseResult result = btParser.parseByte(receivedByte, currentFrame);

        if (result != ParseResult::NEED_MORE_DATA)
        {
            handleParseResult(result);
        }
    }

    switch (currentloopstate)
    {
    case loopstate::loop_state_gif:
        // GIF播放模式
        updateGIF();
        break;
    case loopstate::loop_state_transfer:
     if(!SerialBT.available())
    {
        if(check_packetloss())
        {
            while (SerialBT.available())
            {
                uint8_t dummy[1024]; // 临时缓冲区
                int bytesToRead = min(SerialBT.available(), 1024);
                SerialBT.readBytes(dummy, bytesToRead); // 批量读取并丢弃
            }
        ea++;
        SerialBT.flush();
        btParser.Error_reset();
        SerialBT.write(BT_ERROR_FRAME_FORMAT);
        }
    }
        // 接收GIF文件
        break;

    case loopstate::loop_state_clock:
            // 时钟模式
            updateGIF();
        if (isClockModeActive()) {
        if (current_display_mode == DISPLAY_MODE_BIG) {
         updateBigScreenDisplay();    // 大屏模式只调用大屏更新
        } else {
          updateTimeLogic();           // 小屏模式只调用小屏更新
            }
        }

        break;

    case loopstate::loop_state_score:
        // 计分模式
        if (isScoreModeActive()) {
        // 调用计分模式的更新函数
        updateScoreDisplay();           // 更新计分显示（冒号闪烁等）
        //printf("🔄 Calling updateHighFrequencyScrolling\n");
        updateHighFrequencyScrolling(); // 调用点2：正式的更新逻辑
        }

        break;

    case loopstate::loop_state_text:
        // 文本模式
        updateAllEffects();  // 更新所有特效
        updateBrightness();  // 更新亮度设置
        updateColors();      // 更新颜色状态
        updateTextDisplay(); // 更新文本显示
        break;

    case loopstate::loop_state_split_display:
        // 🆕 分区显示模式：GIF+文字同时显示
        updateGIF();         // 更新GIF（在分区位置）
        updateAllEffects();  // 更新文字特效
        updateBrightness();  // 更新亮度设置
        updateColors();      // 更新颜色状态
        updateTextDisplay(); // 更新文字（在分区位置）
        break;

    case loopstate::loop_state_background:
        // 背景模式
        // 目前没有具体实现，保留以备将来扩展
        break;

    case loopstate::loop_state_list:
        // 列表模式
        if (isnext_list_name)
        {
            dma_display->clearScreen();
            isnext_list_name = false; // 重置标志
            current_list_mode = processFileList();
        }
        switch (current_list_mode)
        {
        case list_mode::LIST_NO_MODE:
            DEBUG_INFO("list no mode");
            break;

        case list_mode::LIST_GIF_MODE:
            DEBUG_INFO("list GIF mode");
            if (is_gif_exist_listname)
                updateGIF_FOR_list(); // 更新GIF播放状态
            break;

        case list_mode::LIST_TEXT_MODE:
            DEBUG_INFO("list TEXT mode");
            break;

        case list_mode::LIST_UNKNOW:
            DEBUG_WARN("list unknow mode");
            break;

        default:
            break;
        }
        break;

    default:
        break;
    }
}

// 处理解析结果
void handleParseResult(ParseResult result)
{
    switch (result)
    {
    case ParseResult::FRAME_COMPLETE:
        DEBUG_INFO("Received complete frame - Command: 0x%02X, Data length: %d",
                   currentFrame.command, currentFrame.dataLength);
        processBluetoothCommand(currentFrame);
        btParser.reset(); // 重置解析器准备下一帧
        break;

    case ParseResult::FRAME_ERROR:
        Serial.println("Frame format error");
        // 丢弃接收缓冲区中的脏数据
        while (SerialBT.available())
        {
            uint8_t dummy[2048]; // 临时缓冲区
            int bytesToRead = min(SerialBT.available(), 2048);
            SerialBT.readBytes(dummy, bytesToRead); // 批量读取并丢弃
        }

        SerialBT.flush();
        btParser.Error_reset();
        SerialBT.write(BT_ERROR_FRAME_FORMAT);
        break;

    case ParseResult::INVALID_COMMAND:
        Serial.println("Invalid command");
        break;

    case ParseResult::DATA_TOO_LONG:
        Serial.println("Data too long");
        break;

    case ParseResult::NEED_MORE_DATA:
        // 继续等待更多数据，无需处理
        break;

    default:
        Serial.printf("Unknown parse result: %d\n", (int)result);
        break;
    }
}

// 处理蓝牙命令
void processBluetoothCommand(const BluetoothFrame &frame)
{
    if (!frame.isValidCommand())
    {
        Serial.println("错误: 无效的命令帧");
        return;
    }

    switch (frame.command)
    {
    case BT_CMD_SET_DIRECTION:               // 0x00
        switchFromGifToTextMode("方向设置"); // 🔧 关键修复：统一处理状态切换
        handleDirectionCommand(BT_DIRECTION_HORIZONTAL);
        Serial.println("设置文本显示方向: 正向显示");
        break;

    case BT_CMD_SET_VERTICAL:                // 0x01
        switchFromGifToTextMode("方向设置"); // 🔧 关键修复：统一处理状态切换
        handleDirectionCommand(BT_DIRECTION_VERTICAL);
        Serial.println("设置文本显示方向: 竖向显示");
        break;

    case BT_CMD_SET_FONT_16x16: // 0x02
        Serial.println("设置字体: 16x16");
        switchFromGifToTextMode("字体切换"); // 🔧 关键修复：统一处理状态切换
        resetAllStatesOnFontSwitch();        // 重置所有继承状态
        currentFontSize = BT_FONT_16x16;
        handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
        break;

    case BT_CMD_SET_FONT_32x32: // 0x03
        Serial.println("设置字体: 32x32");
        switchFromGifToTextMode("字体切换"); // 🔧 关键修复：统一处理状态切换
        resetAllStatesOnFontSwitch();        // 重置所有继承状态
        currentFontSize = BT_FONT_32x32;
        handleFullScreenTextCommand(full_text, getFullTextCharCount());
        break;

    case BT_CMD_SET_TEXT: // 0x04
        handleTextCommand(frame);
        break;

    case BT_CMD_SET_ANIMATION: // 0x05
        break;

    case BT_CMD_SET_COLOR: // 0x06
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("颜色设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到颜色命令，保持GIF播放");
        }
        handleColorCommand(frame);
        break;

    case BT_CMD_SET_BRIGHTNESS: // 0x07
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("亮度设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到亮度命令，保持GIF播放");
        }
        handleBrightnessCommand(frame);
        break;

    case BT_CMD_SET_EFFECT: // 0x08
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("特效设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到特效命令，保持GIF播放");
        }
        handleEffectCommand(frame);
        break;

    case BT_CMD_SET_BORDER: // 0x09
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("边框设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到边框命令，保持GIF播放");
        }
        handleBorderCommand(frame);
        break;

    case BT_CMD_SET_SPECIFIC_COLOR: // 0x0A
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("特定颜色设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到特定颜色命令，保持GIF播放");
        }
        handleSpecificColorCommand(frame);
        break;

    case BT_CMD_SET_RANDOM_COLOR: // 0x0B
        // 🔧 关键修复：只有在纯GIF模式下才需要切换状态
        if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
        {
            switchFromGifToTextMode("随机颜色设置");
        }
        else if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            Serial.println("🔧 分区模式下接收到随机颜色命令，保持GIF播放");
        }
        handleRandomColorCommand(frame);
        break;

    case BT_CMD_SET_SPLIT_MODE:        // 0x0C
        handleSplitModeCommand(frame); // 🆕 处理分区模式设置命令
        break;

    case BT_CMD_FILE_START:                                // 0x10
        currentloopstate = loopstate::loop_state_transfer; // 切换到文件传输模式
        handleFileStartCommand(frame);
        break;

    case BT_CMD_FILE_DATA: // 0x11
        handleFileDataCommand(frame);
        break;

    case BT_CMD_FILE_END:                                  // 0x12
        currentloopstate = loopstate::loop_state_transfer; // 切换到文件传输模式
        handleFileEndCommand(frame);
        break;

    case BT_CMD_FILE_LIST: // 0x13
        handleFileListCommand(frame);
        break;

    case BT_CMD_FILE_DELETE: // 0x14
        handleFileDeleteCommand(frame);
        break;

    case BT_CMD_PLAY_GIF: // 0x15
        // 🔧 关键修复：根据分区模式状态判断，而不是当前循环状态
        if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            // 分区模式：确保切换到分区显示状态，GIF和文字同时显示
            Serial.println("🔄 分区模式下播放GIF，确保状态为split_display");
            currentloopstate = loopstate::loop_state_split_display; // 确保状态正确
            handlePlayGifCommand(frame);
        }
        else
        {
            // 非分区模式：切换到纯GIF播放模式（原有行为）
            Serial.println("🔄 非分区模式下播放GIF，切换到纯GIF模式");
            currentloopstate = loopstate::loop_state_gif;
            handlePlayGifCommand(frame);
        }
        break;

    case BT_CMD_LIST_START: // 0x30
        Serial.println("处理列表开始命令");
        handleAppListStartCommand(frame);
        break;

    case BT_CMD_LIST_DATA: // 0x31
        Serial.println("处理列表数据命令");
        handleAppListDataCommand(frame);
        break;

    case BT_CMD_LIST_END:                              // 0x32
        currentloopstate = loopstate::loop_state_list; // 切换到列表模式
        handleAppListEndCommand(frame);
        break;

    case BT_CMD_CLOCK_MODE: // 0x20
        exitAllModes(); // 确保退出所有模式，避免状态冲突
        currentloopstate = loopstate::loop_state_clock; // 切换到时钟模式
        clearEntireScreen();
        handleClockModeCommand(frame);
        break;

    case BT_CMD_SCORE_MODE: // 0x21
       
        currentloopstate = loopstate::loop_state_score; // 切换到计分模式
        // 🔥 智能清屏：只在非计分模式时清屏，避免连续命令重复刷新
        if (!isScoreModeActive()) {
            Serial.println("🧹 首次进入计分模式：执行全局清屏");
            clearEntireScreen();
        } else {
            Serial.println("📊 已在计分模式：跳过全局清屏，进行区域更新");
        }
        handleScoreModeCommand(frame);
        break;


    default:
        Serial.printf("Unsupported command:0x%02X\n", frame.command);
        break;
    }
}

// 处理文本命令 (0x04)
void handleTextCommand(const BluetoothFrame &frame)
{
    // 增加内存安全检查和调试信息
    Serial.printf("收到文本命令 - 帧有效性: %s, 数据长度: %d, 拥有数据: %s\n",
                  frame.isValid ? "是" : "否", frame.dataLength, frame.ownsData ? "是" : "否");

    if (!frame.hasValidData())
    {
        Serial.println("错误: 帧数据无效，跳过处理");
        return;
    }

    // 🔧 关键修复：只有在纯GIF模式下才需要切换状态和清除GIF
    if (currentloopstate == loopstate::loop_state_gif && currentSplitMode == SPLIT_MODE_DISABLED)
    {
        // 纯GIF模式下接收到文本命令，需要切换到文本模式
        switchFromGifToTextMode("检测到文本命令");
    }
    else if (currentSplitMode != SPLIT_MODE_DISABLED)
    {
        // 分区模式下接收到文本命令，不切换状态，不清除GIF，只处理文本
        Serial.println("🔧 分区模式下接收到文本命令，保持GIF播放，仅更新文本内容");
    }

    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData32x32(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理32x32文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);
            handleFullScreenTextCommand(fontData, charCount);
        }
        else
        {
            Serial.printf("错误: 32x32字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
    else
    {
        // 16x16字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData16x16(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理16x16文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);

            switch (screenArea)
            {
            case BT_SCREEN_UPPER: // 上半屏
                handleUpperTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_LOWER: // 下半屏
                handleLowerTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_BOTH: // 全屏 (分为上下两部分)
            {
                int halfCount = charCount / 2;
                const uint16_t *upperData = fontData;
                const uint16_t *lowerData = fontData + (halfCount * 16); // 每个字符16个uint16_t
                Serial.printf("全屏模式拆分 - 上半屏: %d字符, 下半屏: %d字符\n", halfCount, charCount - halfCount);
                handleTextCommand(upperData, halfCount, lowerData, charCount - halfCount);
            }
            break;
            default:
                Serial.printf("错误: 无效的屏幕区域 0x%02X\n", screenArea);
                break;
            }
        }
        else
        {
            Serial.printf("错误: 16x16字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
}

// 独立处理上半屏文本（保持下半屏不变）
void handleUpperTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置上半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新上半屏文本时清除上半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到上半屏文本变更，清除上半屏特定字符颜色");
        clearUpperSpecificColors();
    }

    // 只释放上半屏数据
    if (dynamic_upper_text)
    {
        free(dynamic_upper_text);
        dynamic_upper_text = nullptr;
    }

    // 分配并复制上半屏数据
    int upperDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_upper_text = (uint16_t *)malloc(upperDataSize);
    if (dynamic_upper_text)
    {
        memcpy(dynamic_upper_text, fontData, upperDataSize);
        dynamic_upper_char_count = charCount;
        Serial.printf("上半屏数据已更新: %d字符, %d字节\n", charCount, upperDataSize);
    }
    else
    {
        Serial.println("错误: 上半屏数据内存分配失败");
        dynamic_upper_char_count = 0;
    }

    // 更新显示状态（只重置上半屏索引）
    textState.upperIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 独立处理下半屏文本（保持上半屏不变）
void handleLowerTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置下半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新下半屏文本时清除下半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到下半屏文本变更，清除下半屏特定字符颜色");
        clearLowerSpecificColors();
    }

    // 只释放下半屏数据
    if (dynamic_lower_text)
    {
        free(dynamic_lower_text);
        dynamic_lower_text = nullptr;
    }

    // 分配并复制下半屏数据
    int lowerDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_lower_text = (uint16_t *)malloc(lowerDataSize);
    if (dynamic_lower_text)
    {
        memcpy(dynamic_lower_text, fontData, lowerDataSize);
        dynamic_lower_char_count = charCount;
        Serial.printf("下半屏数据已更新: %d字符, %d字节\n", charCount, lowerDataSize);
    }
    else
    {
        Serial.println("错误: 下半屏数据内存分配失败");
        dynamic_lower_char_count = 0;
    }

    // 更新显示状态（只重置下半屏索引）
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 处理边框命令 (0x09)
void handleBorderCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_BORDER_DATA_LEN)
    {
        Serial.printf("错误: 边框命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_BORDER_DATA_LEN);
        return;
    }

    uint8_t style, colorIndex, effect, speed;
    frame.getBorderData(style, colorIndex, effect, speed);

    Serial.printf("🎨 收到边框命令 - 样式:%d, 颜色索引:%d, 效果:%d, 速度:%d\n",
                  style, colorIndex, effect, speed);

    // 验证参数有效性
    if (style > BORDER_STYLE_RAINBOW)
    {
        Serial.printf("错误: 无效的边框样式 %d (有效范围: 0-4)\n", style);
        return;
    }

    if (colorIndex > 6)
    {
        Serial.printf("错误: 无效的颜色索引 %d (有效范围: 0-6)\n", colorIndex);
        return;
    }

    if (effect > 3)
    {
        Serial.printf("错误: 无效的边框效果 %d (有效范围: 0-3)\n", effect);
        return;
    }

    if (speed < 1 || speed > 10)
    {
        Serial.printf("错误: 无效的边框速度 %d (有效范围: 1-10)\n", speed);
        return;
    }

    // 根据边框样式进行逻辑处理
    if (style == BORDER_STYLE_NONE)
    {
        // 无边框：清除边框效果
        clearBorderEffect();
        Serial.println("✅ 边框已清除");
        return;
    }

    // 确定颜色
    uint16_t borderColor = COLOR_WHITE; // 默认颜色
    if (style == BORDER_STYLE_RAINBOW)
    {
        // 彩虹边框：颜色参数无效，使用默认白色（实际会被彩虹色覆盖）
        borderColor = COLOR_WHITE;
        Serial.println("📝 彩虹边框模式：忽略颜色选择参数");
    }
    else
    {
        // 其他边框：使用颜色索引映射
        borderColor = BORDER_COLORS[colorIndex];
        Serial.printf("📝 使用颜色: 0x%04X\n", borderColor);
    }

    // 角落边框的效果限制
    if (style == BORDER_STYLE_CORNER && (effect == 1 || effect == 2))
    {
        // 角落边框不支持流动效果，转为静止显示
        effect = 0;
        Serial.println("📝 角落边框不支持流动效果，已转为静止显示");
    }

    // 应用边框设置
    setBorderEffect(style, borderColor, effect, speed);

    // 输出最终设置信息
    const char *styleNames[] = {"无边框", "实线", "点线", "角落", "彩虹"};
    const char *colorNames[] = {"红", "绿", "蓝", "黄", "紫", "青", "白"};
    const char *effectNames[] = {"静止", "顺时针", "逆时针", "闪烁"};

    Serial.printf("✅ 边框设置完成 - %s边框, %s色, %s效果, 速度%d\n",
                  styleNames[style],
                  (style == BORDER_STYLE_RAINBOW) ? "彩虹" : colorNames[colorIndex],
                  effectNames[effect], speed);
}

// 处理特定字符颜色命令 (0x0A)
void handleSpecificColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_SPECIFIC_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 特定字符颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_SPECIFIC_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t charIndex = frame.data[1];
    uint8_t r = frame.data[2];
    uint8_t g = frame.data[3];
    uint8_t b = frame.data[4];

    Serial.printf("🎨 收到特定字符颜色命令 - 区域:%d, 索引:%d, RGB:(%d,%d,%d)\n",
                  screenArea, charIndex, r, g, b);

    // 验证参数有效性 - 32x32字体支持全屏设置
    if (screenArea != BT_SCREEN_UPPER && screenArea != BT_SCREEN_LOWER && screenArea != BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 不支持的屏幕区域 %d (支持上半屏=1, 下半屏=2, 全屏=3)\n", screenArea);
        return;
    }

    // 转换RGB888到RGB565
    uint16_t color = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);

    // 设置特定字符颜色
    setSpecificCharColor(screenArea, charIndex, color);

    const char *areaName = (screenArea == BT_SCREEN_UPPER) ? "上半屏" : (screenArea == BT_SCREEN_LOWER) ? "下半屏"
                                                                                                        : "全屏";
    Serial.printf("✅ 特定字符颜色设置完成 - 区域:%s, 索引:%d, 颜色:0x%04X\n",
                  areaName, charIndex, color);
}

// 处理随机颜色命令 (0x0B)
void handleRandomColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_RANDOM_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 随机颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_RANDOM_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t mode = frame.data[1];
    uint8_t interval = frame.data[2];
    uint8_t seed = frame.data[3];

    Serial.printf("🎲 收到随机颜色命令 - 区域:%d, 模式:%d, 间隔:%d, 种子:%d\n",
                  screenArea, mode, interval, seed);

    // 验证参数有效性 - 随机颜色仅支持全屏设置
    if (screenArea != BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 随机颜色仅支持全屏设置，收到区域参数: %d (应为3)\n", screenArea);
        return;
    }

    if (mode > RANDOM_COLOR_BRIGHT)
    {
        Serial.printf("错误: 无效的随机颜色模式 %d (有效范围: 0-6)\n", mode);
        return;
    }

    if (interval < 1 || interval > 10)
    {
        Serial.printf("错误: 无效的更新间隔 %d (有效范围: 1-10)\n", interval);
        return;
    }

    // 设置随机颜色模式
    setRandomColorMode(screenArea, mode, interval, seed);

    const char *areaNames[] = {"", "上半屏", "下半屏", "全屏"};
    const char *modeNames[] = {"关闭", "每字符不同", "全体相同", "彩虹色", "暖色系", "冷色系", "高亮度"};

    Serial.printf("✅ 随机颜色设置完成 - %s, %s模式, 间隔%d秒, 种子%d\n",
                  areaNames[screenArea], modeNames[mode], interval, seed);
}

// 🔧 辅助函数实现：统一处理从GIF模式切换到文本模式
void switchFromGifToTextMode(const char *reason)
{
    if (currentloopstate == loopstate::loop_state_gif)
    {
        // 🔧 修复：根据当前分区模式状态决定切换到哪种模式
        if (currentSplitMode != SPLIT_MODE_DISABLED)
        {
            // 如果当前有分区模式设置，切换到分区显示模式
            Serial.printf("🔄 %s，从GIF模式切换到分区显示模式\n", reason);
            currentloopstate = loopstate::loop_state_split_display;
        }
        else
        {
            // 如果没有分区模式设置，切换到纯文本模式
            Serial.printf("🔄 %s，从GIF模式切换到文本模式\n", reason);
            currentloopstate = loopstate::loop_state_text;
        }

        // 🔧 完整清理逻辑：确保状态切换的干净性
        if (gifPlaying)
        {
            Serial.println("🛑 停止当前播放的GIF");
            stopGIF(); // 停止当前播放的GIF
        }

        // 清除屏幕，为新的显示模式做准备
        dma_display->clearScreen();
        Serial.println("🧹 已清除屏幕");
    }
}

// 🆕 切换到分区显示模式的函数（保留向后兼容）
void switchToSplitDisplayMode(const char *reason)
{
    Serial.printf("🔄 %s，切换到分区显示模式\n", reason);
    // 默认使用GIF左侧+32x32文字右侧模式
    setSplitDisplayLayout(SPLIT_MODE_GIF_LEFT_TEXT32, reason);
}

// 🆕 完善的分区显示布局设置函数
void setSplitDisplayLayout(uint8_t mode, const char *reason)
{
    Serial.printf("🔄 %s，设置分区显示布局模式: 0x%02X\n", reason, mode);

    // 🔧 优化：检查是否为相同模式，避免不必要的清除操作
    if (currentSplitMode == mode)
    {
        Serial.printf("✅ 当前已处于模式 0x%02X，跳过清理操作\n", mode);
        return; // 相同模式，直接返回，不执行任何清理操作
    }

    Serial.printf("🔄 模式切换：从 0x%02X 切换到 0x%02X\n", currentSplitMode, mode);

    // 🔧 关键修复：仅在模式真正切换时才执行完整清理逻辑
    Serial.println("🧹 开始状态切换清理...");

    // 1. 停止当前播放的任何GIF（解决开机GIF和分区切换残留问题）
    if (gifPlaying)
    {
        Serial.println("🛑 停止当前播放的GIF");
        stopGIF();
    }

    // 2. 完全清除屏幕（确保没有任何残留内容）
    dma_display->clearScreen();
    Serial.println("🧹 已完全清除屏幕");

    // 3. 🔧 新增：清除边框效果（解决分区切换时边框残留问题）
    clearBorderEffect();
    Serial.println("🧹 已清除边框效果");

    // 4. 🔧 新增：强制清除文字数据（解决相同字体大小间数据共享问题）
    freeDynamicTextData();
    Serial.println("🧹 已清除所有动态文字数据");

    // 5. 🔧 新增：重置颜色状态（解决分区切换时颜色状态残留问题）
    switchColorMode(COLOR_MODE_FIXED);
    Serial.println("🧹 已重置颜色状态为固定色模式");

    // 6. 重置文字显示状态（避免旧状态干扰新布局）
    textState.upperIndex = 0;
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
    Serial.println("🔄 已重置文字显示状态");

    switch (mode)
    {
    case SPLIT_MODE_DISABLED:
        // 退出分区模式，恢复全屏模式
        setSplitMode(false, GIF_POSITION_LEFT);
        setScreenParams(SCREEN_WIDTH, SCREEN_HEIGHT, 0, 0);
        currentloopstate = loopstate::loop_state_text;
        currentFontSize = BT_FONT_16x16; // 🔧 重置字体大小为默认值（16x16）
        Serial.println("✅ 已退出分区模式，恢复全屏显示，字体重置为16x16");
        break;

    case SPLIT_MODE_GIF_LEFT_TEXT32:
        // GIF左侧 + 32x32文字右侧
        currentloopstate = loopstate::loop_state_split_display;
        setSplitMode(true, GIF_POSITION_LEFT);
        setScreenParams(SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT, SPLIT_TEXT_LEFT_X, SPLIT_TEXT_Y);
        currentFontSize = BT_FONT_32x32;
        Serial.printf("✅ GIF左侧(%d,%d,%d,%d) + 32x32文字右侧(%d,%d,%d,%d)\n",
                      SPLIT_GIF_LEFT_X, SPLIT_GIF_Y, SPLIT_GIF_SIZE, SPLIT_GIF_SIZE,
                      SPLIT_TEXT_LEFT_X, SPLIT_TEXT_Y, SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT);
        break;

    case SPLIT_MODE_GIF_LEFT_TEXT16:
        // GIF左侧 + 16x16双行文字右侧
        currentloopstate = loopstate::loop_state_split_display;
        setSplitMode(true, GIF_POSITION_LEFT);
        setScreenParams(SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT, SPLIT_TEXT_LEFT_X, SPLIT_TEXT_Y);
        currentFontSize = BT_FONT_16x16;
        Serial.printf("✅ GIF左侧(%d,%d,%d,%d) + 16x16双行文字右侧(%d,%d,%d,%d)\n",
                      SPLIT_GIF_LEFT_X, SPLIT_GIF_Y, SPLIT_GIF_SIZE, SPLIT_GIF_SIZE,
                      SPLIT_TEXT_LEFT_X, SPLIT_TEXT_Y, SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT);
        break;

    case SPLIT_MODE_GIF_RIGHT_TEXT32:
        // GIF右侧 + 32x32文字左侧
        currentloopstate = loopstate::loop_state_split_display;
        setSplitMode(true, GIF_POSITION_RIGHT);
        setScreenParams(SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT, SPLIT_TEXT_RIGHT_X, SPLIT_TEXT_Y);
        currentFontSize = BT_FONT_32x32;
        Serial.printf("✅ GIF右侧(%d,%d,%d,%d) + 32x32文字左侧(%d,%d,%d,%d)\n",
                      SPLIT_GIF_RIGHT_X, SPLIT_GIF_Y, SPLIT_GIF_SIZE, SPLIT_GIF_SIZE,
                      SPLIT_TEXT_RIGHT_X, SPLIT_TEXT_Y, SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT);
        break;

    case SPLIT_MODE_GIF_RIGHT_TEXT16:
        // GIF右侧 + 16x16双行文字左侧
        currentloopstate = loopstate::loop_state_split_display;
        setSplitMode(true, GIF_POSITION_RIGHT);
        setScreenParams(SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT, SPLIT_TEXT_RIGHT_X, SPLIT_TEXT_Y);
        currentFontSize = BT_FONT_16x16;
        Serial.printf("✅ GIF右侧(%d,%d,%d,%d) + 16x16双行文字左侧(%d,%d,%d,%d)\n",
                      SPLIT_GIF_RIGHT_X, SPLIT_GIF_Y, SPLIT_GIF_SIZE, SPLIT_GIF_SIZE,
                      SPLIT_TEXT_RIGHT_X, SPLIT_TEXT_Y, SPLIT_TEXT_WIDTH, SPLIT_TEXT_HEIGHT);
        break;

    default:
        Serial.printf("❌ 无效的分区模式: 0x%02X\n", mode);
        return;
    }

    // 🔧 更新当前分区模式状态
    currentSplitMode = mode;
    Serial.printf("✅ 分区模式状态已更新为: 0x%02X\n", currentSplitMode);

    // 触发文字重绘
    textState.needUpdate = true;

    // 根据新字体大小重新处理文字数据
    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体模式：使用全屏数据
        if (dynamic_full_text)
        {
            handleFullScreenTextCommand(dynamic_full_text, dynamic_full_char_count);
        }
        else
        {
            handleFullScreenTextCommand(full_text, getFullTextCharCount());
        }
    }
    else if (currentFontSize == BT_FONT_16x16)
    {
        // 16x16字体模式：使用上下半屏数据
        if (dynamic_upper_text || dynamic_lower_text)
        {
            handleTextCommand(dynamic_upper_text ? dynamic_upper_text : upper_text,
                              dynamic_upper_char_count > 0 ? dynamic_upper_char_count : getUpperTextCharCount(),
                              dynamic_lower_text ? dynamic_lower_text : lower_text,
                              dynamic_lower_char_count > 0 ? dynamic_lower_char_count : getLowerTextCharCount());
        }
        else
        {
            // 🔧 退出分区模式时，确保使用默认文字数据
            handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
        }
    }
}

// 🆕 处理分区模式命令
void handleSplitModeCommand(const BluetoothFrame &frame)
{
    if (frame.dataLength < BT_SPLIT_MODE_DATA_LEN)
    {
        Serial.printf("❌ 分区模式数据长度不足，需要%d字节，收到%d字节\n",
                      BT_SPLIT_MODE_DATA_LEN, frame.dataLength);
        return;
    }

    uint8_t splitMode = frame.getSplitModeData();

    // 验证模式范围
    if (splitMode > SPLIT_MODE_GIF_RIGHT_TEXT16)
    {
        Serial.printf("❌ 无效的分区模式: 0x%02X\n", splitMode);
        return;
    }

    const char *modeNames[] = {
        "退出分区模式",
        "GIF左侧+32x32文字右侧",
        "GIF左侧+16x16双行文字右侧",
        "GIF右侧+32x32文字左侧",
        "GIF右侧+16x16双行文字左侧"};

    Serial.printf("🔄 收到分区模式设置命令: 0x%02X (%s)\n",
                  splitMode, modeNames[splitMode]);

    // 应用分区模式设置
    setSplitDisplayLayout(splitMode, "蓝牙命令");
}

// 处理时钟模式命令 (0x20)
void handleClockModeCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_CLOCK_DATA_LEN)
    {
        Serial.printf("错误: 时钟模式命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_CLOCK_DATA_LEN);
        return;
    }

    // 🔥 新增：在处理命令前先安全切换到时钟模式，避免残留像素
    safeModeSwitchToClockMode();

    uint8_t screenMode, gifSelect, weekdayR, weekdayG, weekdayB;
    uint8_t timeR, timeG, timeB, hour, minute, weekday, language;

    frame.getClockModeData(screenMode, gifSelect, weekdayR, weekdayG, weekdayB,
                          timeR, timeG, timeB, hour, minute, weekday, language);

    Serial.printf("🕐 收到时钟模式命令\n");
    Serial.printf("  屏幕模式: %s\n", (screenMode == 0) ? "小屏" : "大屏");
    Serial.printf("  GIF选择: %d (预留)\n", gifSelect);
    Serial.printf("  星期颜色: RGB(%d,%d,%d)\n", weekdayR, weekdayG, weekdayB);
    Serial.printf("  时间颜色: RGB(%d,%d,%d)\n", timeR, timeG, timeB);
    Serial.printf("  时间: %02d:%02d\n", hour, minute);
    Serial.printf("  星期: %d\n", weekday);
    Serial.printf("  语言: %s\n", (language == 0) ? "中文" : "英文");

    // 调用时钟模式处理函数
    handleBluetoothClockMode(screenMode, gifSelect, weekdayR, weekdayG, weekdayB,
                            timeR, timeG, timeB, hour, minute, weekday, language);

    Serial.println("✅ 时钟模式命令处理完成");
}

// 处理计分模式命令 (0x21)
void handleScoreModeCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData())
    {
        Serial.printf("错误: 计分模式命令数据无效\n");
        return;
    }

    // 🔥 智能模式切换：只在必要时进行模式切换，避免重复清屏
    if (!isScoreModeActive()) {
        Serial.println("🔄 切换到计分模式");
        safeModeSwitchToScoreMode();
    } else {
        Serial.println("📊 已在计分模式，直接处理命令");
    }

    // 根据数据长度判断是文本命令还是数字命令
    if (frame.dataLength >= BT_SCORE_TEXT_DATA_LEN_MIN && frame.dataLength <= BT_SCORE_TEXT_DATA_LEN_MAX)
    {
        // 文本命令处理 - 支持动态长度（最多10个字符，320字节）
        uint8_t region, colorR, colorG, colorB;
        uint8_t textData[BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES];  // 最大320字节缓冲区
        uint16_t textDataLength;
        frame.getScoreModeTextDataWithColor(region, colorR, colorG, colorB, textData, textDataLength);

        Serial.printf("📝 收到计分模式文本命令\n");
        Serial.printf("  区域: %d (%s)\n", region,
                     (region == 0) ? "左上" : (region == 2) ? "右上" : "未知");
        Serial.printf("  颜色: RGB(%d,%d,%d)\n", colorR, colorG, colorB);
        Serial.printf("  文本数据长度: %d 字节\n", textDataLength);
        Serial.printf("  字符数量: %d 个\n", textDataLength / BT_SCORE_CHAR_BYTES);

        // 调用修正后的计分模式文本处理函数
        handleBluetoothScoreModeText(region, colorR, colorG, colorB, textData, textDataLength);

        Serial.println("✅ 计分模式文本命令处理完成");
    }
    else if (frame.dataLength == BT_SCORE_NUM_DATA_LEN)
    {
        // 数字命令处理
        uint8_t region, colorR, colorG, colorB, operation;
        uint16_t initValue, operValue;
        frame.getScoreModeNumData(region, colorR, colorG, colorB, initValue, operation, operValue);

        Serial.printf("🔢 收到计分模式数字命令\n");
        Serial.printf("  区域: %d (%s)\n", region,
                     (region == 1) ? "左下" : (region == 3) ? "右下" : "未知");
        Serial.printf("  颜色: RGB(%d,%d,%d)\n", colorR, colorG, colorB);
        Serial.printf("  初始值: %d\n", initValue);
        Serial.printf("  操作: %s %d\n", (operation == 0) ? "加" : "减", operValue);

        // 调用计分模式数字处理函数
        handleBluetoothScoreModeNumber(region, colorR, colorG, colorB, initValue, operation, operValue);

        Serial.println("✅ 计分模式数字命令处理完成");
    }
    else
    {
        Serial.printf("错误: 计分模式命令数据长度无效 - 数据长度: %d, 期望: %d~%d(文本) 或 %d(数字)\n",
                      frame.dataLength, BT_SCORE_TEXT_DATA_LEN_MIN, BT_SCORE_TEXT_DATA_LEN_MAX, BT_SCORE_NUM_DATA_LEN);
    }
}

/**
 * 安全切换到计分模式
 * 先清理所有模式，再进入计分模式，避免残留像素
 */
void safeModeSwitchToScoreMode() {
    printf("🔄 安全切换到计分模式...\n");

    // 1. 退出所有其他模式
    exitAllModes();

    // 2. 短暂延时确保清理完成
    delay(50);

    // 3. 初始化并进入计分模式
    if (initScoreMode()) {
        enterScoreMode();
        printf("✅ 已安全切换到计分模式\n");
    } else {
        printf("❌ 错误: 计分模式初始化失败\n");
    }
}


void safeModeSwitchToClockMode() {
    printf("🔄 安全切换到时钟模式...\n");

    // 1. 退出所有其他模式
    exitAllModes();

    // 2. 短暂延时确保清理完成
    delay(50);

    // 3. 进入时钟模式
    enterClockMode();

    printf("✅ 已安全切换到时钟模式\n");
}
// ==================== 全局清理函数实现 ====================

/**
 * 全局清屏函数 - 清除整个显示屏的所有像素
 * 用于解决模式切换时的残留像素问题
 */
void clearEntireScreen() {
    extern MatrixPanel_I2S_DMA* dma_display;
    if (dma_display) {
        printf("🧹 执行全局清屏...\n");
        dma_display->fillScreen(COLOR_BLACK);
        // 也可以使用 dma_display->clearScreen();
        printf("✅ 全局清屏完成\n");
    } else {
        printf("❌ 错误: 显示驱动未初始化\n");
    }
}
/**
 * 统一退出所有模式函数
 * 确保完全清理所有模式的显示和状态
 */
void exitAllModes() {
    printf("🔄 开始退出所有模式...\n");

    // 检查并退出时钟模式
    if (isClockModeActive()) {
        printf("  📅 退出时钟模式\n");
        exitClockMode();
    }

    // 检查并退出计分模式
    if (isScoreModeActive()) {
        printf("  🏆 退出计分模式\n");
        exitScoreMode();
    }

    // 🔥 新增: 清理所有文本区域数据
    // printf("  📝 清理文本区域数据\n");
    // freeAllTextRegionsData();

    // 全局清屏确保没有残留
    clearEntireScreen();

    printf("✅ 所有模式已安全退出\n");
}


