#ifndef SCORE_MODE_H
#define SCORE_MODE_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include <clock_mode.h>


// 屏幕尺寸定义
#define SCREEN_WIDTH 64
#define SCREEN_HEIGHT 32

// 计分模式配置参数
#define SCORE_REGION_WIDTH          24
#define SCORE_REGION_HEIGHT         16
#define SCORE_SCROLL_INTERVAL_MS    500
#define SCORE_BLINK_INTERVAL_MS     1000
#define SCORE_MAX_VALUE             999

// 颜色定义
#define COLOR_BLACK   0x0000
#define COLOR_WHITE   0xFFFF
#define COLOR_RED     0xF800
#define COLOR_GREEN   0x07E0
#define COLOR_BLUE    0x001F
#define COLOR_YELLOW  0xFFE0
#define COLOR_CYAN    0x07FF
#define COLOR_MAGENTA 0xF81F

// 区域类型定义 - 🔥 重构: 扩展为统一的显示区域管理
typedef enum {
    // 原有计分模式区域
    SCORE_REGION_LEFT_TEXT,     // 左上文字区域 (0,0)-(23,15)
    SCORE_REGION_LEFT_SCORE,    // 左下计分区域 (0,16)-(23,31)
    SCORE_REGION_COLON,         // 中间冒号区域 (24,0)-(39,31)
    SCORE_REGION_RIGHT_TEXT,    // 右上文字区域 (40,0)-(63,15)
    SCORE_REGION_RIGHT_SCORE,   // 右下计分区域 (40,16)-(63,31)

    // // 🔥 新增: 文本模式专用区域 (基于分区逻辑的上下屏控制)
    // TEXT_REGION_UPPER,          // 上半屏文本区域 (0,0)-(63,15)
    // TEXT_REGION_LOWER,          // 下半屏文本区域 (0,16)-(63,31)
    // TEXT_REGION_FULL,           // 全屏文本区域 (0,0)-(63,31) - 32x32字体使用
    // //gif+文本混合模式
    //  // 模式1: 左侧32x32 GIF + 右侧上下两个16x16文本区域
    // MODE1_GIF_REGION,           // GIF区域 (0,0)-(31,31)
    // MODE1_TEXT_UPPER,           // 右上文本区域 (32,0)-(63,15)
    // MODE1_TEXT_LOWER,           // 右下文本区域 (32,16)-(63,31)
    
    // // 模式2: 左侧32x32 GIF + 右侧单个32x32文本区域
    // MODE2_GIF_REGION,           // GIF区域 (0,0)-(31,31)
    // MODE2_TEXT_REGION,          // 右侧文本区域 (32,0)-(63,31)
    
    // // 模式3: 右侧32x32 GIF + 左侧上下两个16x16文本区域
    // MODE3_TEXT_UPPER,           // 左上文本区域 (0,0)-(31,15)
    // MODE3_TEXT_LOWER,           // 左下文本区域 (0,16)-(31,31)
    // MODE3_GIF_REGION,           // GIF区域 (32,0)-(63,31)
    
    // // 模式4: 右侧32x32 GIF + 左侧单个32x32文本区域
    // MODE4_TEXT_REGION,          // 左侧文本区域 (0,0)-(31,31)
    // MODE4_GIF_REGION,           // GIF区域 (32,0)-(63,31)
   

    UNIFIED_REGION_COUNT        // 🔥 总区域数量
} UnifiedRegion;                // 🔥 统一的显示区域枚举 (避免与clock_mode.h冲突)

// 🔥 兼容性别名: 保持原有代码兼容
typedef UnifiedRegion ScoreRegion;
#define SCORE_REGION_COUNT UNIFIED_REGION_COUNT

// 区域信息结构
typedef struct {
    uint8_t x1, y1, x2, y2;     // 区域边界
    bool visible;               // 是否可见
} ScoreRegionInfo;

// 颜色配置结构
typedef struct {
    uint16_t left_text_color;   // 左侧文字颜色
    uint16_t right_text_color;  // 右侧文字颜色
    uint16_t left_score_color;  // 左侧计分颜色
    uint16_t right_score_color; // 右侧计分颜色
    uint16_t colon_color;       // 冒号颜色
} ScoreRegionColors;

// ==================== 统一滚动架构（移植自demo(4)） ====================
// 注意：内部使用SimpleScrollInfo结构体，在.cpp文件中定义，外部无需访问

// ==================== 外部显示驱动接口 ====================
// 显示驱动通过extern声明在需要的地方引用

// ==================== 模式控制接口 ====================
bool initScoreMode();                              // 初始化计分模式
void enterScoreMode();                             // 进入计分模式
void exitScoreMode();                              // 退出计分模式
bool isScoreModeActive();                          // 检查是否在计分模式

// ==================== 独立模式禁用控制 ====================
void disableTimeDisplayMode();                     // 禁用时间显示模式
void enableTimeDisplayMode();                      // 启用时间显示模式
void disableScoreDisplayMode();                    // 禁用计分显示模式
void enableScoreDisplayMode();                     // 启用计分显示模式
bool isTimeDisplayEnabled();                       // 查询时间显示是否启用
bool isScoreDisplayEnabled();                      // 查询计分显示是否启用



// ==================== 中文字符显示接口 ====================
void setScoreLeftChineseText(uint8_t char_count);     // 显示左侧中文（"机电队"）
void setScoreRightChineseText(uint8_t char_count);    // 显示右侧中文（"物电队"）

// ==================== 计分控制接口 ====================
void setScoreLeftValue(uint16_t score);           // 设置左侧分数
void setScoreRightValue(uint16_t score);          // 设置右侧分数
void incrementScoreLeft();                         // 左侧分数+1
void incrementScoreRight();                        // 右侧分数+1
void resetScoreLeft();                             // 重置左侧分数
void resetScoreRight();                            // 重置右侧分数
void resetAllScores();                             // 重置所有分数

// ==================== 新增：灵活增减分数接口 ====================
void incrementScoreLeftBy(uint16_t value);        // 左侧增加指定值
void incrementScoreRightBy(uint16_t value);       // 右侧增加指定值
void decrementScoreLeftBy(uint16_t value);        // 左侧减少指定值
void decrementScoreRightBy(uint16_t value);       // 右侧减少指定值

// ==================== 分数获取接口 ====================
uint16_t getScoreLeftValue();                      // 获取左侧分数
uint16_t getScoreRightValue();                     // 获取右侧分数

// ==================== 颜色控制接口 ====================
void setScoreLeftTextColor(uint16_t color);       // 设置左侧文字颜色
void setScoreRightTextColor(uint16_t color);      // 设置右侧文字颜色
void setScoreLeftCounterColor(uint16_t color);    // 设置左侧计分颜色
void setScoreRightCounterColor(uint16_t color);   // 设置右侧计分颜色
void setScoreColonColor(uint16_t color);          // 设置冒号颜色

// ==================== 特效控制接口 ====================
void setScoreColonBlinking(bool enable);          // 设置冒号闪烁
void setScoreTextScrollSpeed(uint16_t speed_ms);   // 设置滚动速度（ms/像素）

// ==================== 显示更新接口 ====================
void updateScoreDisplay();                        // 更新整个计分显示
void updateHighFrequencyScrolling();              // 🔥 高频滚动更新（主循环调用）
void clearAllScoreRegions();                      // 清除所有计分区域

// ==================== 调试接口 ====================
void printScoreModeStatus();                      // 打印计分模式状态
// void testScoreMode();                              // 测试计分模式功能

// ==================== 智能文本显示接口 ====================
// void testSmartTextDisplay();                      // 智能文本显示测试函数
uint16_t createMockTextPacket(uint8_t* buffer, uint8_t region_select, uint8_t char_count,
                             uint8_t language, uint16_t color, const uint8_t* char_data);
const uint8_t* getMockChineseDataForRegion(ScoreRegion region, uint8_t char_count);
void handleSmartTextCommand(uint8_t* data, uint16_t length);
void applySmartTextDisplay(uint8_t* char_data, uint8_t char_count,
                          uint16_t color, uint8_t language, ScoreRegion region);
void setupStaticTextDisplay(uint8_t* char_data, uint8_t char_count, uint16_t color, ScoreRegion region);
void setupScrollingTextDisplay(uint8_t* char_data, uint8_t char_count, uint16_t color, ScoreRegion region);
void loadTextBuffer(ScoreRegion region, uint8_t* char_data, uint8_t char_count, uint16_t color);
void clearTextBuffer(ScoreRegion region);
bool isTextBufferActive(ScoreRegion region);
void renderStaticSmartText(ScoreRegion region, uint8_t* char_data, uint8_t char_count,
                          int16_t offset_x, uint16_t color);
void drawSmartChineseChar(ScoreRegion region, uint8_t char_idx, int16_t virtualX, uint16_t color);
void clearSmartTextDisplay(ScoreRegion region);

// ==================== 🔥 新增: 统一文本区域管理接口 ====================
// 基于分区逻辑的文本区域控制 (重构上下屏显示功能的核心接口)
void clearTextRegion(UnifiedRegion region);                                    // 清除指定文本区域
// void updateTextRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color); // 更新文本区域数据
// void drawTextInRegion(UnifiedRegion region);                                   // 绘制指定区域的文本
// bool isTextRegion(UnifiedRegion region);                                       // 判断是否为文本区域
const ScoreRegionInfo* getRegionInfo(UnifiedRegion region);                   // 获取区域信息

// 🔥 文本区域数据管理
// void setRegionTextData(UnifiedRegion region, const uint16_t* fontData, int charCount); // 设置区域文本数据
const uint16_t* getRegionTextData(UnifiedRegion region, int* charCount);      // 获取区域文本数据
void freeRegionTextData(UnifiedRegion region);                                // 释放区域文本数据

// 🔥 文本区域状态管理
void setRegionTextColor(UnifiedRegion region, uint16_t color);                // 设置区域文本颜色
uint16_t getRegionTextColor(UnifiedRegion region);                            // 获取区域文本颜色
void setRegionNeedUpdate(UnifiedRegion region, bool needUpdate);              // 设置区域更新标志
bool getRegionNeedUpdate(UnifiedRegion region);                               // 获取区域更新标志

// 🔥 文本区域显示更新管理
// void updateTextRegionsDisplay();                                              // 更新所有文本区域显示
// bool hasTextRegionNeedUpdate();                                               // 检查是否有文本区域需要更新
// void clearAllTextRegions();                                                   // 清除所有文本区域
// void freeAllTextRegionsData();                                                // 释放所有文本区域数据
// bool isTextRegion(int region);
// ==================== 🔥 新增: 完整特效系统迁移 ====================
// 分组切换管理
// void setRegionGroupSwitching(UnifiedRegion region, bool enable);              // 启用/禁用区域分组切换
// void updateRegionGroupSwitching(UnifiedRegion region);                        // 更新区域分组切换
int getRegionCurrentGroup(UnifiedRegion region);                              // 获取当前分组索引
int getRegionTotalGroups(UnifiedRegion region);                               // 获取总分组数

// 滚动特效管理
void setRegionScrollEffect(UnifiedRegion region, bool enable, uint8_t scrollType, uint8_t speed); // 设置区域滚动特效
// void updateRegionScrolling(UnifiedRegion region);                             // 更新区域滚动特效
void clearRegionScrollEffect(UnifiedRegion region);                           // 清除区域滚动特效

// 闪烁特效管理
// void setRegionBlinkEffect(UnifiedRegion region, bool enable, uint8_t speed);  // 设置区域闪烁特效
// void updateRegionBlinking(UnifiedRegion region);                              // 更新区域闪烁特效
// void clearRegionBlinkEffect(UnifiedRegion region);                            // 清除区域闪烁特效

// 统一特效更新
// void updateAllRegionEffects();                                                // 更新所有区域特效
// void clearAllRegionEffects(UnifiedRegion region);                             // 清除指定区域所有特效

// ==================== 🔥 字体尺寸获取辅助函数 ====================
// static bool is32x32FontRegion(UnifiedRegion region);
// static int getFontSize(UnifiedRegion region);
// static int getFontHeight(UnifiedRegion region);
// static int getFontWidth(UnifiedRegion region);
// ==================== 🔥 特效状态结构体声明 ====================
// 分组切换状态管理
typedef struct {
    bool auto_switch;           // 自动切换启用
    int current_group;          // 当前分组索引
    int total_groups;           // 总分组数
    unsigned long last_switch;  // 上次切换时间
    unsigned long switch_interval; // 切换间隔 (毫秒)
} RegionGroupState;

// 滚动特效状态管理
typedef struct {
    bool active;                // 滚动激活状态
    uint8_t scroll_type;        // 滚动类型 (左/右/上/下)
    uint8_t speed;              // 滚动速度 (0-10)
    int scroll_offset;          // 当前滚动偏移
    unsigned long last_update;  // 上次更新时间
} RegionScrollState;

// 闪烁特效状态管理
typedef struct {
    bool active;                // 闪烁激活状态
    uint8_t speed;              // 闪烁速度 (0-10)
    bool visible;               // 当前可见状态
    unsigned long last_blink;   // 上次闪烁时间
} RegionBlinkState;

// 特效状态数组声明
extern RegionGroupState region_groups[UNIFIED_REGION_COUNT];
extern RegionScrollState region_scrolls[UNIFIED_REGION_COUNT];
extern RegionBlinkState region_blinks[UNIFIED_REGION_COUNT];

// ==================== 蓝牙控制接口 ====================
void handleBluetoothScoreModeText(uint8_t region, uint8_t colorR, uint8_t colorG, uint8_t colorB,
                                 const uint8_t* textData, uint16_t textDataLength); // 修正版本，支持RGB颜色
void handleBluetoothScoreModeNumber(uint8_t region, uint8_t colorR, uint8_t colorG, uint8_t colorB,
                                   uint16_t initValue, uint8_t operation, uint16_t operValue);

// 🔧 调试和测试函数
void testBluetoothTextScrolling();
// void testTextRegionSystem();                                                  // 🔥 测试分区逻辑文本显示系统
// void testCompleteRegionMigration();                                           // 🔥 完全迁移验证测试
// 大屏模式全局状态管理 - 外部声明
extern DisplayMode current_display_mode;
extern BigScreenState big_screen_state;
extern unsigned long big_screen_last_switch;
extern unsigned long big_screen_switch_interval;
extern WeekdayLanguage big_screen_language;
extern uint16_t big_screen_time_color;
extern uint16_t big_screen_weekday_color;
extern uint16_t big_screen_bg_color;
#endif // SCORE_MODE_H








//gif文本混合模式调用链
// 主循环 loop()
//     ↓
// updateAllRegionEffects()  // 每次循环都调用
//     ↓
// updateRegionScrolling(region)  // 更新scroll_offset
//     ↓
// setRegionNeedUpdate(region, true)  // 标记区域需要更新
//     ↓
// hasTextRegionNeedUpdate()  // 主循环检查是否有更新
//     ↓
// updateTextRegionsDisplay()  // 执行显示更新
//     ↓
// drawTextInRegion(region)  // 绘制指定区域
//     ↓
// drawText16x16InRegionWithEffects() 或 drawText32x32InRegionWithEffects()
//     ↓
// drawScrollingText16x16InRegion() 或 drawScrollingText32x32InRegion()