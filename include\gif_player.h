#ifndef GIF_PLAYER_H
#define GIF_PLAYER_H

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "AnimatedGIF.h"
#include "LittleFS.h"
#include "config.h"
#include "esp_heap_caps.h"

// GIF播放相关变量声明
extern MatrixPanel_I2S_DMA *dma_display;
extern AnimatedGIF gif;
extern File gifFile;
extern bool gifPlaying;
extern unsigned long lastGifFrame;
extern int gifFrameDelay;
extern int x_offset, y_offset; // 添加偏移量变量

// LED矩阵屏和GIF相关函数声明
bool initLEDMatrix();
bool playGIF(const char *filename);
bool playGIFStream(const char *filename); // 新增流式播放函数
bool playGIFAuto(const char *filename);   // 智能选择播放模式
void stopGIF();
void GIFDraw(GIFDRAW *pDraw);
void updateGIF();
void printMemoryInfo();                // 添加内存信息打印函数
bool canLoadGIF(const char *filename); // 检查GIF文件是否可以加载
void forceGarbageCollection();         // 强制垃圾回收
bool changeStartupGIF(uint8_t number); // 修改开机GIF
// 流式GIF播放回调函数
void *GIFOpenFile(const char *fname, int32_t *pSize);
void GIFCloseFile(void *pHandle);
int32_t GIFReadFile(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen);
int32_t GIFSeekFile(GIFFILE *pFile, int32_t iPosition);
void setGifDisplayOffset(int offset_x, int offset_y); // 设置gif初始坐标基准点

/* ------------------------------------------------------------------------
 * 分区显示模式相关函数
 * ------------------------------------------------------------------------ */
// 分区模式状态变量
extern uint8_t gifPosition; // GIF位置：0=左边，1=右边
extern bool isSplitMode;    // 是否为分区显示模式

// 分区模式函数
void setSplitMode(bool enabled, uint8_t position); // 设置分区模式和GIF位置
void updateSplitModeOffsets();                     // 更新分区模式下的偏移量
bool isGifInSplitMode();                           // 检查是否在分区模式
void validateGifSizeForSplit();                    // 验证GIF尺寸是否适合分区模式

#endif // GIF_PLAYER_H
