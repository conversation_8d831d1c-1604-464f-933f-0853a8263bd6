#include "score_mode.h"
#include "config.h"
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
// 外部字体数据声明 - 引用clock_mode.cpp中定义的字体数据
extern const unsigned char font_data[11][16];  // 8×16数字字体数据

// ==================== 静态变量定义 ====================
static bool score_mode_active = false;
static uint16_t left_score = 0;
static uint16_t right_score = 0;

// 独立模式控制标志
bool time_display_enabled = true;  // 移除static，使其可被其他模块访问
static bool score_display_enabled = true;
// dma_display在LEDController.cpp中定义，这里只需要extern声明
extern MatrixPanel_I2S_DMA *dma_display;
// 🔥 重构: 统一的显示区域坐标定义 (扩展支持文本模式)
static const ScoreRegionInfo display_regions[UNIFIED_REGION_COUNT] = {
    // 原有计分模式区域
    {0,  0,  23, 15, true},     // SCORE_REGION_LEFT_TEXT: 左上文字区域
    {0,  16, 23, 31, true},     // SCORE_REGION_LEFT_SCORE: 左下计分区域
    {24, 0,  39, 31, true},     // SCORE_REGION_COLON: 中间冒号区域
    {40, 0,  63, 15, true},     // SCORE_REGION_RIGHT_TEXT: 右上文字区域
    {40, 16, 63, 31, true},     // SCORE_REGION_RIGHT_SCORE: 右下计分区域

    // // 🔥 新增: 文本模式专用区域 (基于分区逻辑的上下屏控制)
    // {0,  0,  63, 15, true},     // TEXT_REGION_UPPER: 上半屏文本区域 (全宽)
    // {0,  16, 63, 31, true},     // TEXT_REGION_LOWER: 下半屏文本区域 (全宽)
    // {0,  0,  63, 31, true},      // TEXT_REGION_FULL: 全屏文本区域 (32x32字体)
    //     // 模式1区域定义
    // {0,  0,  31, 31, true},     // MODE1_GIF_REGION: 左侧GIF区域
    // {32, 0,  63, 15, true},     // MODE1_TEXT_UPPER: 右上文本区域 (16x16字体)
    // {32, 16, 63, 31, true},     // MODE1_TEXT_LOWER: 右下文本区域 (16x16字体)
    
    // // 模式2区域定义
    // {0,  0,  31, 31, true},     // MODE2_GIF_REGION: 左侧GIF区域
    // {32, 0,  63, 31, true},     // MODE2_TEXT_REGION: 右侧文本区域 (32x32字体)
    
    // // 模式3区域定义
    // {0,  0,  31, 15, true},     // MODE3_TEXT_UPPER: 左上文本区域 (16x16字体)
    // {0,  16, 31, 31, true},     // MODE3_TEXT_LOWER: 左下文本区域 (16x16字体)
    // {32, 0,  63, 31, true},     // MODE3_GIF_REGION: 右侧GIF区域
    
    // // 模式4区域定义
    // {0,  0,  31, 31, true},     // MODE4_TEXT_REGION: 左侧文本区域 (32x32字体)
    // {32, 0,  63, 31, true}      // MODE4_GIF_REGION: 右侧GIF区域
};

// 🔥 兼容性别名: 保持原有代码兼容
static const ScoreRegionInfo* score_regions = display_regions;

// 默认颜色配置
static ScoreRegionColors colors = {
    .left_text_color = COLOR_WHITE,
    .right_text_color = COLOR_WHITE,
    .left_score_color = COLOR_GREEN,
    .right_score_color = COLOR_RED,
    .colon_color = COLOR_YELLOW
};

// 🔥 统一滚动架构：简化的滚动信息（移植自demo(4)）
typedef struct {
    int16_t virtualX;           // 虚拟X坐标
    uint32_t lastMoveTime;      // 上次移动时间
    uint16_t moveSpeed;         // 移动速度（ms/像素）
    uint8_t char_count;         // 字符数量
    uint16_t totalWidth;        // 总宽度
    bool isScrolling;           // 是否滚动
    bool isWaiting;             // 是否等待重置
    uint32_t waitStartTime;     // 等待开始时间
} SimpleScrollInfo;

static SimpleScrollInfo simple_left_scroll = {0};
static SimpleScrollInfo simple_right_scroll = {0};

// 冒号闪烁控制
static bool colon_visible = true;
static bool colon_blinking = false;
static unsigned long last_blink_time = 0;

// 16x32冒号点阵数据（你提供的数据）
static const unsigned char dot_16x32_char[64] = {
    // 第一个字符的数据（共64字节）
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// ==================== 动态文本缓冲区系统 ====================
typedef struct {
    uint8_t char_data[10][32];  // 最多10个字符的点阵数据
    uint8_t char_count;         // 实际字符数量
    uint16_t color;             // 显示颜色
    bool is_active;             // 是否有有效数据
} DynamicTextBuffer;

static DynamicTextBuffer left_text_buffer = {0};   // 左侧文本缓冲区
static DynamicTextBuffer right_text_buffer = {0};  // 右侧文本缓冲区

// 缓冲区操作函数
static DynamicTextBuffer* getTextBuffer(ScoreRegion region) {
    if (region == SCORE_REGION_LEFT_TEXT) {
        return &left_text_buffer;
    } else if (region == SCORE_REGION_RIGHT_TEXT) {
        return &right_text_buffer;
    }
    return nullptr;
}

// ==================== 🔥 新增: 统一文本区域数据管理 ====================
// 文本区域数据存储 (基于分区逻辑的数据管理)
typedef struct {
    uint16_t* font_data;        // 字体数据指针
    int char_count;             // 字符数量
    uint16_t text_color;        // 文本颜色
    bool need_update;           // 更新标志
    bool is_active;             // 是否激活
} TextRegionData;

// 文本区域数据数组 (对应TEXT_REGION_*区域)
static TextRegionData text_region_data[UNIFIED_REGION_COUNT] = {0};

// 🔥 前向声明: 文本绘制辅助函数
// static void drawText16x16InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color);
// static void drawText32x32InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color);
// static void drawText16x16InRegionWithEffects(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color);
// static void drawText32x32InRegionWithEffects(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color);
// static void drawCharacterInRegion(const ScoreRegionInfo* info, int16_t char_x, int16_t char_y, const uint16_t* char_data, uint16_t color);
// static void drawScrollingText16x16InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color, int startCharIndex, int displayCharCount);
// static void drawCharacter32x32InRegion(const ScoreRegionInfo* info, int16_t char_x, int16_t char_y, const uint16_t* char_data, uint16_t color);
// static void drawScrollingText32x32InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color, int startCharIndex, int displayCharCount);

// 🔥 统一文本区域管理接口实现
void clearTextRegion(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return;

    const ScoreRegionInfo* info = &display_regions[region];
    extern MatrixPanel_I2S_DMA* dma_display;

    // 清除指定区域的所有像素
    dma_display->fillRect(info->x1, info->y1,
                     info->x2 - info->x1 + 1, info->y2 - info->y1 + 1,
                     COLOR_BLACK);
    printf("🧹 Cleared text region %d: (%d,%d)-(%d,%d)\n",
           region, info->x1, info->y1, info->x2, info->y2);
}

// void updateTextRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     printf("🔄 Updating text region %d: %d chars, color=0x%04X\n", region, charCount, color);
//     printf("🔍 DEBUG: updateTextRegion - 区域: %d, 数据指针: %p, 字符数: %d, 颜色: 0x%04X\n",
//            region, fontData, charCount, color);
//     if (fontData && charCount > 0) {
//         printf("🔍 DEBUG: 字体数据前3个值: 0x%04X, 0x%04X, 0x%04X\n",
//                fontData[0], charCount > 1 ? fontData[16] : 0, charCount > 2 ? fontData[32] : 0);
//     }

//     // 清除旧数据
//     freeRegionTextData(region);

//     // 设置新数据
//     setRegionTextData(region, fontData, charCount);
//     setRegionTextColor(region, color);
//     setRegionNeedUpdate(region, true);

//     // 立即清除并重绘区域
//     clearTextRegion(region);
//     drawTextInRegion(region);
// }

// void drawTextInRegion(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     TextRegionData* data = &text_region_data[region];
//     printf("🔍 DEBUG: drawTextInRegion - 区域: %d, 激活状态: %s, 数据指针: %p, 字符数: %d\n",
//            region, data->is_active ? "true" : "false", data->font_data, data->char_count);

//     if (!data->is_active || !data->font_data || data->char_count <= 0) {
//         printf("🔍 DEBUG: 跳过绘制 - 数据无效或未激活\n");
//         return;
//     }

//     // 🔥 闪烁特效检查
//     RegionBlinkState* blink = &region_blinks[region];
//     if (blink->active && !blink->visible) {
//         printf("🔍 DEBUG: 跳过绘制 - 闪烁特效隐藏状态\n");
//         return; // 闪烁隐藏状态，不绘制
//     }

//     const ScoreRegionInfo* info = &display_regions[region];
//     extern MatrixPanel_I2S_DMA* dma_display;

//     printf("🎨 Drawing text in region %d: %d chars\n", region, data->char_count);
//     printf("🔍 DEBUG: 开始实际绘制 - 区域边界: (%d,%d)-(%d,%d), 颜色: 0x%04X\n",
//            info->x1, info->y1, info->x2, info->y2, data->text_color);

//     // 🔥 根据区域类型和特效选择绘制方式
//     if (region == TEXT_REGION_UPPER || region == TEXT_REGION_LOWER||
//         region == MODE1_TEXT_UPPER || region == MODE1_TEXT_LOWER ||
//         region == MODE3_TEXT_UPPER || region == MODE3_TEXT_LOWER) {
//         // 16x16字体绘制 (上下半屏)
//         drawText16x16InRegionWithEffects(region, data->font_data, data->char_count, data->text_color);
//     } else if (region == TEXT_REGION_FULL||region == MODE2_TEXT_REGION||region == MODE4_TEXT_REGION) {
//         // 32x32字体绘制 (全屏)
//         drawText32x32InRegionWithEffects(region, data->font_data, data->char_count, data->text_color);
//     }

//     data->need_update = false;
// }

// bool isTextRegion(UnifiedRegion region) {
//     return (region == TEXT_REGION_UPPER ||
//             region == TEXT_REGION_LOWER ||
//             region == TEXT_REGION_FULL||
//             region == MODE1_TEXT_UPPER || region == MODE1_TEXT_LOWER ||
//             region == MODE2_TEXT_REGION ||
//             region == MODE3_TEXT_UPPER || region == MODE3_TEXT_LOWER ||
//             region == MODE4_TEXT_REGION);
// }

// const ScoreRegionInfo* getRegionInfo(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return nullptr;
//     return &display_regions[region];
// }

// 🔥 文本区域数据管理实现
// void setRegionTextData(UnifiedRegion region, const uint16_t* fontData, int charCount) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     printf("🔍 DEBUG: setRegionTextData - 区域: %d, 输入数据指针: %p, 字符数: %d\n",
//            region, fontData, charCount);

//     TextRegionData* data = &text_region_data[region];

//     // 释放旧数据
//     if (data->font_data) {
//         free(data->font_data);
//         data->font_data = nullptr;
//     }

    // // 🔥 根据字体类型计算数据大小
    // int dataSize;
    // if (region == TEXT_REGION_FULL||region == MODE2_TEXT_REGION||region == MODE4_TEXT_REGION) {
    //     dataSize = charCount * 64 * sizeof(uint16_t); // 32x32字体: 64个uint16_t每字符
    // } else {
    //     dataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体: 16个uint16_t每字符
    // }

    // 分配并复制数据
//     data->font_data = (uint16_t*)malloc(dataSize);
//     if (data->font_data) {
//         memcpy(data->font_data, fontData, dataSize);
//         data->char_count = charCount;
//         data->is_active = true;
//         printf("✅ Set region %d text data: %d chars, %d bytes\n", region, charCount, dataSize);
//         printf("🔍 DEBUG: 内存分配成功 - 存储地址: %p, 复制完成, 激活状态: true\n", data->font_data);
//         if (data->font_data && charCount > 0) {
//             printf("🔍 DEBUG: 存储后数据验证 - 前3个值: 0x%04X, 0x%04X, 0x%04X\n",
//                    data->font_data[0], charCount > 1 ? data->font_data[16] : 0, charCount > 2 ? data->font_data[32] : 0);
//         }
//     } else {
//         printf("❌ Failed to allocate memory for region %d\n", region);
//         printf("🔍 DEBUG: 内存分配失败 - 请求大小: %d bytes\n", dataSize);
//         data->char_count = 0;
//         data->is_active = false;
//     }
// }

const uint16_t* getRegionTextData(UnifiedRegion region, int* charCount) {
    if (region >= UNIFIED_REGION_COUNT || !charCount) return nullptr;

    TextRegionData* data = &text_region_data[region];
    *charCount = data->char_count;

    printf("🔍 DEBUG: getRegionTextData - 区域: %d, 返回字符数: %d, 数据指针: %p, 激活状态: %s\n",
           region, data->char_count, data->font_data, data->is_active ? "true" : "false");

    if (data->font_data && data->char_count > 0) {
        printf("🔍 DEBUG: 读取数据验证 - 前3个值: 0x%04X, 0x%04X, 0x%04X\n",
               data->font_data[0], data->char_count > 1 ? data->font_data[16] : 0, data->char_count > 2 ? data->font_data[32] : 0);
    } else {
        printf("🔍 DEBUG: 无有效数据 - 数据指针为空或字符数为0\n");
    }

    return data->font_data;
}

void freeRegionTextData(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return;

    TextRegionData* data = &text_region_data[region];
    if (data->font_data) {
        free(data->font_data);
        data->font_data = nullptr;
    }
    data->char_count = 0;
    data->is_active = false;
    data->need_update = false;
}

// 🔥 文本区域状态管理实现
void setRegionTextColor(UnifiedRegion region, uint16_t color) {
    if (region >= UNIFIED_REGION_COUNT) return;
    text_region_data[region].text_color = color;
}

uint16_t getRegionTextColor(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return COLOR_WHITE;
    return text_region_data[region].text_color;
}

void setRegionNeedUpdate(UnifiedRegion region, bool needUpdate) {
    if (region >= UNIFIED_REGION_COUNT) return;
    text_region_data[region].need_update = needUpdate;
}

bool getRegionNeedUpdate(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return false;
    return text_region_data[region].need_update;
}

// ==================== 🔥 文本绘制辅助函数实现 ====================

// // 🔥 16x16字体区域绘制 (基于分区逻辑的文本显示) - 基础版本
// static void drawText16x16InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     const ScoreRegionInfo* info = &display_regions[region];
//     extern MatrixPanel_I2S_DMA* dma_display;

//     printf("🎨 Drawing 16x16 text in region %d: %d chars, color=0x%04X\n", region, charCount, color);

//     // 🔥 基于区域的字符绘制 (支持分组显示)
//     const int maxCharsPerGroup = (info->x2 - info->x1 + 1) / 16; // 根据区域宽度计算每组字符数
//     int startCharIndex = 0; // 基础版本不支持分组

//     for (int charIdx = 0; charIdx < charCount && charIdx < maxCharsPerGroup; charIdx++) {
//         int actualCharIdx = startCharIndex + charIdx;
//         if (actualCharIdx >= charCount) break;

//         // 计算字符在区域内的位置
//         int16_t char_x = info->x1 + (charIdx * 16);
//         int16_t char_y = info->y1;

//         // 边界检查
//         if (char_x + 15 > info->x2) break;

//         // 绘制单个16x16字符
//         const uint16_t* char_data = fontData + (actualCharIdx * 16);
//         drawCharacterInRegion(info, char_x, char_y, char_data, color);
//     }

//     printf("✅ 16x16 text drawing completed for region %d\n", region);
// }

// 🔥 16x16字体区域绘制 (支持完整特效) - 完整版本
// static void drawText16x16InRegionWithEffects(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     const ScoreRegionInfo* info = &display_regions[region];
//     RegionGroupState* group = &region_groups[region];
//     RegionScrollState* scroll = &region_scrolls[region];

//     printf("🎨 Drawing 16x16 text with effects in region %d: %d chars\n", region, charCount);

//     // 🔥 计算显示参数
//     const int maxCharsPerGroup = (info->x2 - info->x1 + 1) / 16;
//     int startCharIndex = group->auto_switch ? (group->current_group * maxCharsPerGroup) : 0;
//     int displayCharCount = (charCount - startCharIndex > maxCharsPerGroup) ? maxCharsPerGroup : (charCount - startCharIndex);

//     // 🔥 滚动特效处理
//     if (scroll->active) {
//         drawScrollingText16x16InRegion(region, fontData, charCount, color, startCharIndex, displayCharCount);
//     } else {
//         // 🔥 普通绘制 (支持分组)
//         for (int charIdx = 0; charIdx < displayCharCount; charIdx++) {
//             int actualCharIdx = startCharIndex + charIdx;
//             if (actualCharIdx >= charCount) break;

//             int16_t char_x = info->x1 + (charIdx * 16);
//             int16_t char_y = info->y1;

//             if (char_x + 15 > info->x2) break;

//             const uint16_t* char_data = fontData + (actualCharIdx * 16);
//             drawCharacterInRegion(info, char_x, char_y, char_data, color);
//         }
//     }

//     printf("✅ 16x16 text with effects drawing completed for region %d\n", region);
// }

// // 🔥 字符绘制辅助函数 (像素级安全绘制) - 恢复原始列取模算法
// static void drawCharacterInRegion(const ScoreRegionInfo* info, int16_t char_x, int16_t char_y,
//                                   const uint16_t* char_data, uint16_t color) {
//     extern MatrixPanel_I2S_DMA* dma_display;

//     // 🔥 恢复原始算法：使用列取模方式，每个uint16_t代表一列的16个像素
//     for (int col = 0; col < 16; col++) {
//         uint16_t col_data = char_data[col];  // 按列读取数据

//         for (int row = 0; row < 16; row++) {
//             int16_t pixel_x = char_x + col;
//             int16_t pixel_y = char_y + row;

//             // 🔥 保留：像素级边界检查 - 绝对安全
//             if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
//                 pixel_y >= info->y1 && pixel_y <= info->y2) {

//                 // 🔥 恢复原始位操作：从高位开始，高位对应上方像素
//                 if (col_data & (0x8000 >> row)) {
//                     dma_display->drawPixel(pixel_x, pixel_y, color);
//                 }
//             }
//             // 超出边界的像素自动跳过
//         }
//     }
// }

// // 🔥 滚动文本绘制函数
// static void drawScrollingText16x16InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount,
//                                            uint16_t color, int startCharIndex, int displayCharCount) {
//     const ScoreRegionInfo* info = &display_regions[region];
//     RegionScrollState* scroll = &region_scrolls[region];

//     // 🔥 计算滚动起始位置
//     int16_t start_x = info->x1;
//     int16_t start_y = info->y1;

//     switch (scroll->scroll_type) {
//         case 1: // 向左滚动
//             start_x = info->x1 + (info->x2 - info->x1 + 1) - scroll->scroll_offset;
//             break;
//         case 2: // 向右滚动
//             start_x = info->x1 - (displayCharCount * 16) + scroll->scroll_offset;  // 🔥 修复：从左侧外开始向右移动
//             break;
//         case 3: // 向上滚动
//             start_y = info->y1 + 16 - scroll->scroll_offset;
//             break;
//         case 4: // 向下滚动
//             start_y = info->y1 - 16 + scroll->scroll_offset;  // 🔥 修复：从上方开始向下移动
//             break;
//     }
// // ✅ 完整的区域清除
//         dma_display->fillRect(info->x1, info->y1,
//                      info->x2 - info->x1 + 1, info->y2 - info->y1 + 1,
//                      COLOR_BLACK);  
//     // 🔥 绘制所有字符 (边界检查会自动处理可见性)
//     for (int charIdx = 0; charIdx < displayCharCount; charIdx++) {
//         int actualCharIdx = startCharIndex + charIdx;
//         if (actualCharIdx >= charCount) break;

//         int16_t char_x = start_x + (charIdx * 16);
//         int16_t char_y = start_y;

//         const uint16_t* char_data = fontData + (actualCharIdx * 16);
//         drawCharacterInRegion(info, char_x, char_y, char_data, color);
//     }
// }

// // 🔥 32x32字体区域绘制 (基于分区逻辑的全屏文本显示) - 基础版本
// static void drawText32x32InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     const ScoreRegionInfo* info = &display_regions[region];
//     extern MatrixPanel_I2S_DMA* dma_display;

//     printf("🎨 Drawing 32x32 text in region %d: %d chars, color=0x%04X\n", region, charCount, color);

//     // 🔥 32x32字体全屏显示 (每屏最多2个字符)
//     const int maxCharsPerScreen = (info->x2 - info->x1 + 1) / 32; // 64/32=2个字符
//     int startCharIndex = 0; // 基础版本不支持分组

//     for (int charIdx = 0; charIdx < charCount && charIdx < maxCharsPerScreen; charIdx++) {
//         int actualCharIdx = startCharIndex + charIdx;
//         if (actualCharIdx >= charCount) break;

//         // 计算字符在区域内的位置 (32x32字符居中显示)
//         int16_t char_x = info->x1 + (charIdx * 32);
//         int16_t char_y = info->y1;

//         // 边界检查
//         if (char_x + 31 > info->x2) break;

//         // 绘制单个32x32字符
//         const uint16_t* char_data = fontData + (actualCharIdx * 64); // 32x32字体每字符64个uint16_t
//         drawCharacter32x32InRegion(info, char_x, char_y, char_data, color);
//     }

//     printf("✅ 32x32 text drawing completed for region %d\n", region);
// }

// // 🔥 32x32字体区域绘制 (支持完整特效) - 完整版本
// static void drawText32x32InRegionWithEffects(UnifiedRegion region, const uint16_t* fontData, int charCount, uint16_t color) {
//     if (region >= UNIFIED_REGION_COUNT || !fontData || charCount <= 0) return;

//     const ScoreRegionInfo* info = &display_regions[region];
//     RegionGroupState* group = &region_groups[region];
//     RegionScrollState* scroll = &region_scrolls[region];

//     printf("🎨 Drawing 32x32 text with effects in region %d: %d chars\n", region, charCount);

//     // 🔥 计算显示参数
//     const int maxCharsPerScreen = (info->x2 - info->x1 + 1) / 32; // 64/32=2个字符
//     int startCharIndex = group->auto_switch ? (group->current_group * maxCharsPerScreen) : 0;
//     int displayCharCount = (charCount - startCharIndex > maxCharsPerScreen) ? maxCharsPerScreen : (charCount - startCharIndex);

//     // 🔥 滚动特效处理
//     if (scroll->active) {
//         drawScrollingText32x32InRegion(region, fontData, charCount, color, startCharIndex, displayCharCount);
//     } else {
//         // 🔥 普通绘制 (支持分组)
//         for (int charIdx = 0; charIdx < displayCharCount; charIdx++) {
//             int actualCharIdx = startCharIndex + charIdx;
//             if (actualCharIdx >= charCount) break;

//             int16_t char_x = info->x1 + (charIdx * 32);
//             int16_t char_y = info->y1;

//             if (char_x + 31 > info->x2) break;

//             const uint16_t* char_data = fontData + (actualCharIdx * 64);
//             drawCharacter32x32InRegion(info, char_x, char_y, char_data, color);
//         }
//     }

//     printf("✅ 32x32 text with effects drawing completed for region %d\n", region);
// }

// // 🔥 32x32字符绘制辅助函数 - 恢复原始列取模算法
// static void drawCharacter32x32InRegion(const ScoreRegionInfo* info, int16_t char_x, int16_t char_y,
//                                        const uint16_t* char_data, uint16_t color) {
//     extern MatrixPanel_I2S_DMA* dma_display;

//     // 🔥 恢复原始算法：使用列取模方式，每个uint16_t代表一列的32个像素
//     for (int col = 0; col < 32; col++) {
//         uint16_t column_data_low = char_data[col * 2];      // 按列读取：低16位
//         uint16_t column_data_high = char_data[col * 2 + 1]; // 按列读取：高16位

//         for (int row = 0; row < 32; row++) {
//             int16_t pixel_x = char_x + col;
//             int16_t pixel_y = char_y + row;

//             // 🔥 保留：像素级边界检查
//             if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
//                 pixel_y >= info->y1 && pixel_y <= info->y2) {

//                 // 🔥 恢复原始位操作：从高位开始
//                 bool pixel_on;
//                 if (row < 16) {
//                     pixel_on = (column_data_low & (0x8000 >> row)) != 0;
//                 } else {
//                     pixel_on = (column_data_high & (0x8000 >> (row - 16))) != 0;
//                 }

//                 if (pixel_on) {
//                     dma_display->drawPixel(pixel_x, pixel_y, color);
//                 }
//             }
//         }
//     }
// }

// // 🔥 32x32滚动文本绘制函数
// static void drawScrollingText32x32InRegion(UnifiedRegion region, const uint16_t* fontData, int charCount,
//                                            uint16_t color, int startCharIndex, int displayCharCount) {
//     const ScoreRegionInfo* info = &display_regions[region];
//     RegionScrollState* scroll = &region_scrolls[region];
//     extern MatrixPanel_I2S_DMA* dma_display;

//     // 🔥 关键优化：一次性填充背景，减少闪烁（参考计分模式renderSimpleScrollingText）
//     uint16_t bgColor = COLOR_BLACK;
//     dma_display->fillRect(info->x1, info->y1,
//                          info->x2 - info->x1 + 1, info->y2 - info->y1 + 1,
//                          bgColor);

//     // 🔥 计算滚动起始位置
//     int16_t start_x = info->x1;
//     int16_t start_y = info->y1;

//     switch (scroll->scroll_type) {
//         case 1: // 向左滚动
//             start_x = info->x1 + (info->x2 - info->x1 + 1) - scroll->scroll_offset;
//             break;
//         case 2: // 向右滚动
//             start_x = info->x1 - (displayCharCount * 32) + scroll->scroll_offset;  // 🔥 修复：从左侧外开始向右移动
//             break;
//         case 3: // 向上滚动
//             start_y = info->y1 + 32 - scroll->scroll_offset;
//             break;
//         case 4: // 向下滚动
//             start_y = info->y1 - 32 + scroll->scroll_offset;  // 🔥 修复：从上方开始向下移动
//             break;
//     }

//     // 🔥 绘制所有字符 (边界检查会自动处理可见性)
//     for (int charIdx = 0; charIdx < displayCharCount; charIdx++) {
//         int actualCharIdx = startCharIndex + charIdx;
//         if (actualCharIdx >= charCount) break;

//         int16_t char_x = start_x + (charIdx * 32);
//         int16_t char_y = start_y;

//         const uint16_t* char_data = fontData + (actualCharIdx * 64);
//         drawCharacter32x32InRegion(info, char_x, char_y, char_data, color);
//     }
// }



// ==================== 🔥 新增: 基于分区逻辑的文本显示更新 ====================
// bool isTextRegion(int region) {
//     // 判断是否是文本区域（排除GIF区域）
//     return region != MODE1_GIF_REGION && 
//            region != MODE2_GIF_REGION && 
//            region != MODE3_GIF_REGION && 
//            region != MODE4_GIF_REGION;
// }


// 🔥 更新所有文本区域的显示 (替代原有的updateTextDisplay逻辑)
// void updateTextRegionsDisplay() {
//     printf("🔍 DEBUG: updateTextRegionsDisplay 被调用\n");

//     // 检查并更新所有文本区域
//     for (int region = 0; region < UNIFIED_REGION_COUNT; region++) {
//         if (isTextRegion(region)) {
//             // 更新逻辑        
//             UnifiedRegion r = (UnifiedRegion)region;
//             bool needUpdate = getRegionNeedUpdate(r);
//             TextRegionData* data = &text_region_data[r];

//             printf("🔍 DEBUG: 检查区域 %d - 需要更新: %s, 激活状态: %s, 数据指针: %p, 字符数: %d\n",
//                 r, needUpdate ? "true" : "false",
//                 data->is_active ? "true" : "false",
//                 data->font_data, data->char_count);

//             if (needUpdate) {
//                 printf("🔄 Updating text region %d display\n", region);
//                 printf("🔍 DEBUG: 开始绘制区域 %d\n", r);
//                 drawTextInRegion(r);
//                 printf("🔍 DEBUG: 区域 %d 绘制完成\n", r);
//             }

//         }
//     }
//     printf("🔍 DEBUG: updateTextRegionsDisplay 完成\n");
// }

// 🔥 检查是否有任何文本区域需要更新
// bool hasTextRegionNeedUpdate() {
//     for (int region = 0; region < UNIFIED_REGION_COUNT; region++) {
//          if (isTextRegion(region)){
//             if (getRegionNeedUpdate((UnifiedRegion)region)) {
//                 return true;
//             }
//         }
//     }
//     return false;
// }

// 🔥 清除所有文本区域
// void clearAllTextRegions() {
//     printf("🧹 Clearing all text regions\n");
//     for (int region = 0; region < UNIFIED_REGION_COUNT; region++) {
//         if (isTextRegion(region)){
//         clearTextRegion((UnifiedRegion)region);
//         }
//     }
// }

// // 🔥 释放所有文本区域数据
// void freeAllTextRegionsData() {
//     printf("🗑️ Freeing all text regions data\n");
//     for (int region = 0; region < UNIFIED_REGION_COUNT; region++) {
//         if (isTextRegion(region)){
//         freeRegionTextData((UnifiedRegion)region);
//         }
//     }
// }

// ==================== 🔥 完整特效系统实现 ====================

// 特效状态数组定义 (结构体在头文件中声明)
// RegionGroupState region_groups[UNIFIED_REGION_COUNT] = {0};
// RegionScrollState region_scrolls[UNIFIED_REGION_COUNT] = {0};
// RegionBlinkState region_blinks[UNIFIED_REGION_COUNT] = {0};

// 🔥 分组切换管理实现
// void setRegionGroupSwitching(UnifiedRegion region, bool enable) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionGroupState* group = &region_groups[region];
//     group->auto_switch = enable;
//     group->current_group = 0;
//     group->last_switch = millis();
//     group->switch_interval = 2000; // 默认2秒切换间隔

//     if (enable) {
//         // 计算总分组数
//         TextRegionData* data = &text_region_data[region];
//         if (data->is_active && data->char_count > 0) {
//             const ScoreRegionInfo* info = &display_regions[region];
//             int maxCharsPerGroup = (info->x2 - info->x1 + 1) / 16; // 假设16x16字体
//             group->total_groups = (data->char_count + maxCharsPerGroup - 1) / maxCharsPerGroup;
//         }
//         printf("🔄 Region %d group switching enabled: %d groups\n", region, group->total_groups);
//     } else {
//         group->total_groups = 1;
//         printf("⏸️ Region %d group switching disabled\n", region);
//     }
// }

// void updateRegionGroupSwitching(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionGroupState* group = &region_groups[region];
//     TextRegionData* data = &text_region_data[region];

//     if (!group->auto_switch || !data->is_active || group->total_groups <= 1) return;

//     unsigned long currentTime = millis();
//     if (currentTime - group->last_switch >= group->switch_interval) {
//         group->current_group++;
//         if (group->current_group >= group->total_groups) {
//             group->current_group = 0;
//         }
//         group->last_switch = currentTime;
//         setRegionNeedUpdate(region, true);
//         printf("🔄 Region %d switched to group %d/%d\n", region, group->current_group + 1, group->total_groups);
//     }
// }

int getRegionCurrentGroup(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return 0;
    return region_groups[region].current_group;
}

int getRegionTotalGroups(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return 1;
    return region_groups[region].total_groups;
}

// 🔥 滚动特效管理实现
void setRegionScrollEffect(UnifiedRegion region, bool enable, uint8_t scrollType, uint8_t speed) {
    if (region >= UNIFIED_REGION_COUNT) return;

    RegionScrollState* scroll = &region_scrolls[region];
    scroll->active = enable;
    scroll->scroll_type = scrollType;
    scroll->speed = speed > 10 ? 10 : speed; // 限制速度范围
    scroll->scroll_offset = 0;
    scroll->last_update = millis();

    if (enable) {
        printf("🌊 Region %d scroll effect enabled: type=%d, speed=%d\n", region, scrollType, speed);
    } else {
        printf("⏸️ Region %d scroll effect disabled\n", region);
    }
}

// //根据区域获取字体大小辅助函数
// static bool is32x32FontRegion(UnifiedRegion region) {
//     return (region == MODE2_TEXT_REGION || region == MODE4_TEXT_REGION);
// }

// static int getFontSize(UnifiedRegion region) {
//     return is32x32FontRegion(region) ? 32 : 16;
// }

// static int getFontHeight(UnifiedRegion region) {
//     return getFontSize(region);
// }

// static int getFontWidth(UnifiedRegion region) {
//     return getFontSize(region);
// }


// void updateRegionScrolling(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionScrollState* scroll = &region_scrolls[region];
//     TextRegionData* data = &text_region_data[region];

//     if (!scroll->active || !data->is_active) return;

//     unsigned long currentTime = millis();
//     unsigned long scrollInterval = 100 - (scroll->speed * 8); // 速度控制: 20-100ms
//     int font_height =0;
//     if (currentTime - scroll->last_update >= scrollInterval) {
//         const ScoreRegionInfo* info = &display_regions[region];
//         int region_width = info->x2 - info->x1 + 1;
//         int font_width = getFontWidth(region);
//         int text_width = data->char_count * font_width;        
//         int moveDistance = (scroll->speed / 2) + 1;

//         // 🔥 智能渲染：记录旧偏移量，只在位置真正改变时才标记更新
//         int old_scroll_offset = scroll->scroll_offset;

//         switch (scroll->scroll_type) {
//             case 1: // 向左滚动
//                 scroll->scroll_offset += moveDistance;
//                 if (scroll->scroll_offset >= region_width + text_width) {
//                     scroll->scroll_offset = -text_width;
//                 }
//                 break;

//             case 2: // 向右滚动
//                 scroll->scroll_offset += moveDistance;  // 🔥 修复：改为递增
//                 if (scroll->scroll_offset >= region_width + text_width) {
//                     scroll->scroll_offset = -text_width;  // 🔥 修复：重置到左侧外
//                 }
//                 break;

//             case 3: // 向上滚动
//                 scroll->scroll_offset += moveDistance;
//                font_height = getFontHeight(region);
//                 if (scroll->scroll_offset >= font_height)
//                 {
//                     scroll->scroll_offset = -font_height;
//                 }
//                 break;

//             case 4: // 向下滚动
//                 scroll->scroll_offset += moveDistance;  // 🔥 修复：改为递增
//                 font_height = getFontHeight(region);
//                 if (scroll->scroll_offset >= font_height)
//                 {
//                     scroll->scroll_offset = -font_height;  // 🔥 修复：重置到上方
//                 }
//                 break;
//         }

//         scroll->last_update = currentTime;

//         // 🔥 关键优化：只在滚动偏移量真正改变时才标记需要更新（参考计分模式智能渲染）
//         if (scroll->scroll_offset != old_scroll_offset) {
//             setRegionNeedUpdate(region, true);
//             printf("🌊 Region %d scroll position changed: %d -> %d\n", region, old_scroll_offset, scroll->scroll_offset);
//         }
//     }
// }

void clearRegionScrollEffect(UnifiedRegion region) {
    if (region >= UNIFIED_REGION_COUNT) return;

    RegionScrollState* scroll = &region_scrolls[region];
    scroll->active = false;
    scroll->scroll_offset = 0;
    setRegionNeedUpdate(region, true);
    printf("🛑 Region %d scroll effect cleared\n", region);
}

// // 🔥 闪烁特效管理实现
// void setRegionBlinkEffect(UnifiedRegion region, bool enable, uint8_t speed) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionBlinkState* blink = &region_blinks[region];
//     blink->active = enable;
//     blink->speed = speed > 10 ? 10 : speed; // 限制速度范围
//     blink->visible = true;
//     blink->last_blink = millis();

//     if (enable) {
//         printf("💫 Region %d blink effect enabled: speed=%d\n", region, speed);
//     } else {
//         printf("⏸️ Region %d blink effect disabled\n", region);
//         setRegionNeedUpdate(region, true); // 确保重新显示
//     }
// }

// void updateRegionBlinking(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionBlinkState* blink = &region_blinks[region];

//     if (!blink->active) return;

//     unsigned long currentTime = millis();
//     unsigned long blinkInterval = 1000 - (blink->speed * 80); // 速度控制: 200-1000ms

//     if (currentTime - blink->last_blink >= blinkInterval) {
//         blink->visible = !blink->visible;
//         blink->last_blink = currentTime;

//         if (blink->visible) {
//             setRegionNeedUpdate(region, true); // 需要重绘
//         } else {
//             clearTextRegion(region); // 清除显示
//         }
//     }
// }

// void clearRegionBlinkEffect(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     RegionBlinkState* blink = &region_blinks[region];
//     blink->active = false;
//     blink->visible = true;
//     setRegionNeedUpdate(region, true);
//     printf("🛑 Region %d blink effect cleared\n", region);
// }

// 🔥 统一特效管理
// void updateAllRegionEffects() {
//     for (int region = 0; region < UNIFIED_REGION_COUNT; region++) {
//         UnifiedRegion r = (UnifiedRegion)region;
//         updateRegionGroupSwitching(r);
//         updateRegionScrolling(r);
//         updateRegionBlinking(r);
//     }
// }

// void clearAllRegionEffects(UnifiedRegion region) {
//     if (region >= UNIFIED_REGION_COUNT) return;

//     setRegionGroupSwitching(region, false);
//     clearRegionScrollEffect(region);
//     clearRegionBlinkEffect(region);
//      clearTextRegion(region);        //清除固定区域像素函数
//     //dma_display->fillScreen(0);   // 强制全屏清除
//     printf("🧹 All effects cleared for region %d\n", region);
// }


// 加载蓝牙数据到缓冲区
void loadTextBuffer(ScoreRegion region, uint8_t* char_data, uint8_t char_count, uint16_t color) {
    DynamicTextBuffer* buffer = getTextBuffer(region);
    if (!buffer || char_count > 10) return;

    // 清除旧数据
    memset(buffer, 0, sizeof(DynamicTextBuffer));

    // 加载新数据
    buffer->char_count = char_count;
    buffer->color = color;
    buffer->is_active = true;

    for (uint8_t i = 0; i < char_count; i++) {
        memcpy(buffer->char_data[i], &char_data[i * 32], 32);
    }

    printf("Loaded %d characters to %s buffer\n", char_count,
           (region == SCORE_REGION_LEFT_TEXT) ? "LEFT" : "RIGHT");
}

// 清除缓冲区（准备下一包）
void clearTextBuffer(ScoreRegion region) {
    DynamicTextBuffer* buffer = getTextBuffer(region);
    if (buffer) {
        memset(buffer, 0, sizeof(DynamicTextBuffer));
        printf("Cleared %s text buffer\n",
               (region == SCORE_REGION_LEFT_TEXT) ? "LEFT" : "RIGHT");
    }
}

// 检查缓冲区状态
bool isTextBufferActive(ScoreRegion region) {
    DynamicTextBuffer* buffer = getTextBuffer(region);
    return buffer ? buffer->is_active : false;
}

// 12x12中文字符点阵数据（你提供的数据）
static const unsigned char left_12x12_chars[7][24] = {
    { // [0] "机"
        0x04, 0x00, 0xE4, 0x01, 0x24, 0x01, 0x2F, 0x01,
        0x24, 0x01, 0x2E, 0x01, 0x36, 0x01, 0x25, 0x01,
        0x24, 0x01, 0x24, 0x05, 0x14, 0x05, 0x0C, 0x06
    },
    { // [1] "电"
        0x10, 0x00, 0x10, 0x00, 0xFF, 0x01, 0x11, 0x01,
        0x11, 0x01, 0xFF, 0x01, 0x11, 0x01, 0x11, 0x01,
        0xFF, 0x01, 0x11, 0x04, 0x10, 0x04, 0xE0, 0x07
    },
    { // [2] "队"
        0x80, 0x00, 0x8F, 0x00, 0x89, 0x00, 0x89, 0x00,
        0x85, 0x00, 0x85, 0x00, 0x49, 0x01, 0x49, 0x01,
        0x49, 0x01, 0x27, 0x02, 0x21, 0x02, 0x11, 0x04
    },
    { // [0] "工"
    0x00, 0x00, 0xFE, 0x03, 0x20, 0x00, 
    0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 
    0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 
    0x20, 0x00, 0x20, 0x00, 0xFF, 0x07
  },
  { // [1] "程"
    0xE8, 0x03, 0x27, 0x02, 0x24, 0x02, 
    0xE4, 0x03, 0x0F, 0x00, 0xE4, 0x03, 
    0x8E, 0x00, 0x96, 0x00, 0xE5, 0x03, 
    0x84, 0x00, 0x84, 0x00, 0xF4, 0x07
  },
  { // [2] "学"
    0x12, 0x02, 0x24, 0x01, 0xFF, 0x07, 
    0x01, 0x04, 0xFC, 0x01, 0x80, 0x00, 
    0x40, 0x00, 0xFF, 0x07, 0x20, 0x00, 
    0x20, 0x00, 0x20, 0x00, 0x38, 0x00
  },
  { // [3] "院"
    0x40, 0x00, 0x87, 0x00, 0xF5, 0x07, 
    0x15, 0x04, 0x03, 0x00, 0xE5, 0x03, 
    0x05, 0x00, 0xF5, 0x07, 0x43, 0x01, 
    0x41, 0x01, 0x21, 0x05, 0x11, 0x06
  }

};

static const unsigned char right_12x12_chars[6][24]={
    { // [3] "物"
        0x24, 0x00, 0x24, 0x00, 0xE5, 0x07, 0x5F, 0x05,
        0x45, 0x05, 0x44, 0x05, 0x2C, 0x05, 0xA7, 0x04,
        0x94, 0x04, 0x44, 0x04, 0x24, 0x04, 0x14, 0x03
    },
    { // [4] "电" (同[1])
        0x10, 0x00, 0x10, 0x00, 0xFF, 0x01, 0x11, 0x01,
        0x11, 0x01, 0xFF, 0x01, 0x11, 0x01, 0x11, 0x01,
        0xFF, 0x01, 0x11, 0x04, 0x10, 0x04, 0xE0, 0x07
    },
    { // [5] "队" (同[2])
        0x80, 0x00, 0x8F, 0x00, 0x89, 0x00, 0x89, 0x00,
        0x85, 0x00, 0x85, 0x00, 0x49, 0x01, 0x49, 0x01,
        0x49, 0x01, 0x27, 0x02, 0x21, 0x02, 0x11, 0x04
    },
    { // [0] "理"
    0xF0, 0x07, 0x97, 0x04, 0x92, 0x04, 
    0xF2, 0x07, 0x92, 0x04, 0x97, 0x04, 
    0xF2, 0x07, 0x82, 0x00, 0xF2, 0x07, 
    0x86, 0x00, 0x83, 0x00, 0xF8, 0x07
    },
    { // [1] "子"
        0x00, 0x00, 0xFC, 0x01, 0x80, 0x00, 
        0x40, 0x00, 0x20, 0x00, 0x20, 0x00, 
        0xFF, 0x07, 0x20, 0x00, 0x20, 0x00, 
        0x20, 0x00, 0x20, 0x00, 0x38, 0x00
    },
    { // [2] "系"
        0xC0, 0x03, 0x3E, 0x00, 0x10, 0x00, 
        0x08, 0x01, 0xFC, 0x00, 0x60, 0x00, 
        0x18, 0x02, 0xFE, 0x07, 0x20, 0x04, 
        0x24, 0x01, 0x22, 0x02, 0x31, 0x04
    }

};
// ==================== 内部辅助函数声明 ====================
static void clearScoreRegion(ScoreRegion region);
static void drawScoreDigits(ScoreRegion region, uint16_t score, uint16_t color);
static void drawDigitToRegion(ScoreRegion region, uint8_t digit, int16_t x_offset, uint16_t color);
// 🔥 统一滚动架构函数声明（移植自demo(4)）
static void draw16x32Colon(uint16_t color);
static void drawSingleChineseChar(ScoreRegion region, uint8_t char_idx, int16_t virtualX, uint16_t color);
static void updateSimpleScrollingPosition(SimpleScrollInfo* scroll_info);
static void renderSimpleScrollingText(SimpleScrollInfo* scroll_info, ScoreRegion region, uint16_t color);

// ==================== 模式控制函数 ====================
bool initScoreMode() {
    printf("Initializing Score Mode...\n");

    // 🔥 统一架构：初始化简化滚动信息
    memset(&simple_left_scroll, 0, sizeof(SimpleScrollInfo));
    memset(&simple_right_scroll, 0, sizeof(SimpleScrollInfo));

    // 设置默认滚动速度：100ms移动1像素（移植自demo(4)）
    simple_left_scroll.moveSpeed = 100;
    simple_right_scroll.moveSpeed = 100;

    // 重置分数
    left_score = 0;
    right_score = 0;

    // 重置冒号状态
    colon_visible = true;
    colon_blinking = false;
    last_blink_time = 0;

    printf("Score Mode initialized successfully\n");
    return true;
}

void enterScoreMode() {
    if (score_mode_active) {
        printf("Already in Score Mode\n");
        return;
    }
    
    printf("Entering Score Mode...\n");
    score_mode_active = true;
    
    // 清除所有区域
    clearAllScoreRegions();
    
    // 绘制初始显示
    drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    draw16x32Colon(colors.colon_color);
    
    printf("Score Mode entered successfully\n");
}

void exitScoreMode() {
    if (!score_mode_active) {
        printf("Not in Score Mode\n");
        return;
    }

    printf("Exiting Score Mode...\n");
    score_mode_active = false;

    // 🔥 统一架构：停止所有滚动
    simple_left_scroll.isScrolling = false;   // 停止左侧滚动
    simple_right_scroll.isScrolling = false;  // 停止右侧滚动

    // 清除所有显示
    clearAllScoreRegions();

    printf("Score Mode exited successfully\n");
}

bool isScoreModeActive() {
    return score_mode_active;
}

// ==================== 独立模式控制函数 ====================
void disableTimeDisplayMode() {
    time_display_enabled = false;
    printf("Time display mode DISABLED\n");
}

void enableTimeDisplayMode() {
    time_display_enabled = true;
    printf("Time display mode ENABLED\n");
}

void disableScoreDisplayMode() {
    score_display_enabled = false;
    printf("Score display mode DISABLED\n");
}

void enableScoreDisplayMode() {
    score_display_enabled = true;
    printf("Score display mode ENABLED\n");
}

bool isTimeDisplayEnabled() {
    return time_display_enabled;
}

bool isScoreDisplayEnabled() {
    return score_display_enabled;
}

// ==================== 计分控制函数 ====================
void setScoreLeftValue(uint16_t score) {
    if (score > SCORE_MAX_VALUE) {
        score = 0;  // 超过999归零
    }

    left_score = score;
    printf("🔢 Left score set to: %d (区域更新模式)\n", left_score);

    if (score_mode_active) {
        printf("📊 更新左下计分区域，其他区域保持不变\n");
        drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    }
}

void setScoreRightValue(uint16_t score) {
    if (score > SCORE_MAX_VALUE) {
        score = 0;  // 超过999归零
    }

    right_score = score;
    printf("🔢 Right score set to: %d (区域更新模式)\n", right_score);

    if (score_mode_active) {
        printf("📊 更新右下计分区域，其他区域保持不变\n");
        drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    }
}

void incrementScoreLeft() {
    // 使用新的通用函数，保持向后兼容
    incrementScoreLeftBy(1);
}

void incrementScoreRight() {
    // 使用新的通用函数，保持向后兼容
    incrementScoreRightBy(1);
}

void resetScoreLeft() {
    left_score = 0;
    printf("Left score reset to 0\n");
    
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    }
}

void resetScoreRight() {
    right_score = 0;
    printf("Right score reset to 0\n");
    
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    }
}

void resetAllScores() {
    printf("Resetting all scores...\n");
    resetScoreLeft();
    resetScoreRight();
}

uint16_t getScoreLeftValue() {
    return left_score;
}

uint16_t getScoreRightValue() {
    return right_score;
}

// ==================== 新增：核心分数更新函数 ====================
// 通用分数更新函数，处理边界和循环逻辑
static void updateScoreValue(uint16_t* score_ptr, int32_t delta, const char* side) {
    int32_t new_score = (int32_t)(*score_ptr) + delta;
    uint16_t old_score = *score_ptr;

    // 处理边界情况 - 实现循环逻辑
    if (new_score < 0) {
        // 减少时的循环：0-1=999, 999-1=998...
        new_score = 1000 + (new_score % 1000);
        if (new_score == 1000) new_score = 0;
    } else if (new_score > SCORE_MAX_VALUE) {
        // 增加时的循环：999+1=0, 0+1=1...
        new_score = new_score % 1000;
    }

    *score_ptr = (uint16_t)new_score;

    // 调试输出
    if (delta > 0) {
        printf("%s score increased by %d: %d -> %d\n", side, (int)delta, old_score, *score_ptr);
    } else {
        printf("%s score decreased by %d: %d -> %d\n", side, (int)(-delta), old_score, *score_ptr);
    }
}

// ==================== 新增：灵活增减分数接口实现 ====================
void incrementScoreLeftBy(uint16_t value) {
    if (value == 0) return;  // 避免无意义操作

    updateScoreValue(&left_score, (int32_t)value, "Left");

    // 更新显示
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    }
}

void incrementScoreRightBy(uint16_t value) {
    if (value == 0) return;  // 避免无意义操作

    updateScoreValue(&right_score, (int32_t)value, "Right");

    // 更新显示
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    }
}

void decrementScoreLeftBy(uint16_t value) {
    if (value == 0) return;  // 避免无意义操作

    updateScoreValue(&left_score, -(int32_t)value, "Left");

    // 更新显示
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    }
}

void decrementScoreRightBy(uint16_t value) {
    if (value == 0) return;  // 避免无意义操作

    updateScoreValue(&right_score, -(int32_t)value, "Right");

    // 更新显示
    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    }
}

// ==================== 颜色控制函数 ====================
void setScoreLeftTextColor(uint16_t color) {
    colors.left_text_color = color;
    printf("Left text color set to: 0x%04X\n", color);
}

void setScoreRightTextColor(uint16_t color) {
    colors.right_text_color = color;
    printf("Right text color set to: 0x%04X\n", color);
}

void setScoreLeftCounterColor(uint16_t color) {
    colors.left_score_color = color;
    printf("Left score color set to: 0x%04X\n", color);

    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_LEFT_SCORE, left_score, colors.left_score_color);
    }
}

void setScoreRightCounterColor(uint16_t color) {
    colors.right_score_color = color;
    printf("Right score color set to: 0x%04X\n", color);

    if (score_mode_active) {
        drawScoreDigits(SCORE_REGION_RIGHT_SCORE, right_score, colors.right_score_color);
    }
}

void setScoreColonColor(uint16_t color) {
    colors.colon_color = color;
    printf("Colon color set to: 0x%04X\n", color);

    if (score_mode_active) {
        draw16x32Colon(colors.colon_color);
    }
}


// ==================== 新的中文字符设置函数 ====================
void setScoreLeftChineseText(uint8_t char_count) {
     if (char_count > 10) char_count = 10;  // 改为最多10个字符

    // 🔥 统一架构：简化的参数设置（移植自demo(4)）
    simple_left_scroll.char_count = char_count;
    simple_left_scroll.totalWidth = char_count * 16;

    // 🔥 修复：确保滚动速度被正确初始化
    if (simple_left_scroll.moveSpeed == 0) {
        simple_left_scroll.moveSpeed = 100;  // 默认100ms/像素
        printf("🔧 LEFT: Initialized moveSpeed to %dms\n", simple_left_scroll.moveSpeed);
    }

    if (simple_left_scroll.totalWidth > SCORE_REGION_WIDTH) {
        // 🔥 滚动模式：从右边界外开始（移植自demo(4)）
        simple_left_scroll.virtualX = SCORE_REGION_WIDTH;
        simple_left_scroll.isScrolling = true;
        simple_left_scroll.isWaiting = false;
        simple_left_scroll.lastMoveTime = millis();
        printf("🔥 LEFT SCROLLING MODE: virtualX=%d, totalWidth=%d, moveSpeed=%dms\n",
               simple_left_scroll.virtualX, simple_left_scroll.totalWidth, simple_left_scroll.moveSpeed);
    } else {
        // 🔥 静态模式：立即显示，无滚动（移植自demo(4)）
        simple_left_scroll.virtualX = 0;
        simple_left_scroll.isScrolling = true;   // 设置为true，让主循环处理
        simple_left_scroll.isWaiting = false;
        simple_left_scroll.moveSpeed = 0;       // 0表示不移动（静态显示）
        simple_left_scroll.lastMoveTime = millis();
        // printf("STATIC MODE: virtualX=%d, moveSpeed=0 (no movement)\n", simple_left_scroll.virtualX);
    }

    // 🔥 关键改进：立即渲染，确保扫到命令就立马显示（移植自demo(4)）
    if (char_count > 0 && score_mode_active) {
        renderSimpleScrollingText(&simple_left_scroll, SCORE_REGION_LEFT_TEXT, colors.left_text_color);
        printf("🔥 LEFT immediate render completed (moveSpeed=%d)\n", simple_left_scroll.moveSpeed);
    }
}

void setScoreRightChineseText(uint8_t char_count) {
    if (char_count > 10) char_count = 10;  // 改为最多10个字符

    // 🔥 统一架构：简化的参数设置（移植自demo(4)）
    simple_right_scroll.char_count = char_count;
    simple_right_scroll.totalWidth = char_count * 16;

    // 🔥 修复：确保滚动速度被正确初始化
    if (simple_right_scroll.moveSpeed == 0) {
        simple_right_scroll.moveSpeed = 100;  // 默认100ms/像素
        printf("🔧 RIGHT: Initialized moveSpeed to %dms\n", simple_right_scroll.moveSpeed);
    }

    if (simple_right_scroll.totalWidth > SCORE_REGION_WIDTH) {
        // 🔥 滚动模式：从右边界外开始（移植自demo(4)）
        simple_right_scroll.virtualX = SCORE_REGION_WIDTH;
        simple_right_scroll.isScrolling = true;
        simple_right_scroll.isWaiting = false;
        simple_right_scroll.lastMoveTime = millis();
        printf("🔥 RIGHT SCROLLING MODE: virtualX=%d, totalWidth=%d, moveSpeed=%dms\n",
               simple_right_scroll.virtualX, simple_right_scroll.totalWidth, simple_right_scroll.moveSpeed);
    } else {
        // 🔥 静态模式：立即显示，无滚动（移植自demo(4)）
        simple_right_scroll.virtualX = 0;
        simple_right_scroll.isScrolling = true;   // 设置为true，让主循环处理
        simple_right_scroll.isWaiting = false;
        simple_right_scroll.moveSpeed = 0;       // 0表示不移动（静态显示）
        simple_right_scroll.lastMoveTime = millis();
        // printf("STATIC MODE: virtualX=%d, moveSpeed=0 (no movement)\n", simple_right_scroll.virtualX);
    }

    // 🔥 关键改进：立即渲染，确保扫到命令就立马显示（移植自demo(4)）
    if (char_count > 0 && score_mode_active) {
        renderSimpleScrollingText(&simple_right_scroll, SCORE_REGION_RIGHT_TEXT, colors.right_text_color);
        printf("🔥 RIGHT immediate render completed (moveSpeed=%d)\n", simple_right_scroll.moveSpeed);
    }
}

// ==================== 特效控制函数 ====================
void setScoreColonBlinking(bool enable) {
    colon_blinking = enable;
    colon_visible = true;  // 重置为可见状态
    last_blink_time = millis();

    printf("Colon blinking %s\n", enable ? "enabled" : "disabled");

    if (score_mode_active) {
        if (enable) {
            draw16x32Colon(colors.colon_color);
        } else {
            draw16x32Colon(colors.colon_color);  // 确保显示
        }
    }
}

void setScoreTextScrollSpeed(uint16_t speed_ms) {
    simple_left_scroll.moveSpeed = speed_ms;
    simple_right_scroll.moveSpeed = speed_ms;
    printf("Scroll speed set to %d ms/pixel\n", speed_ms);
}

// ==================== 显示更新函数 ====================
void updateScoreDisplay() {
    if (!score_mode_active || !score_display_enabled) return;

    // 🔥 统一架构：只处理冒号闪烁，滚动由updateHighFrequencyScrolling处理
    if (colon_blinking) {
        unsigned long now = millis();
        if (now - last_blink_time >= SCORE_BLINK_INTERVAL_MS) {
            colon_visible = !colon_visible;
            last_blink_time = now;

            if (colon_visible) {
                draw16x32Colon(colors.colon_color);
            } else {
                clearScoreRegion(SCORE_REGION_COLON);
            }
        }
    }
}

// 🔥 统一架构：优化的滚动更新（移植自demo(4)，减少过度渲染）
void updateHighFrequencyScrolling() {
    if (!score_mode_active || !score_display_enabled) {
        // printf("🔥 DEBUG: Score mode not active or display disabled\n");
        return;
    }

    // 🔥 智能渲染：记录旧位置，只在位置改变时才重新渲染（移植自demo(4)）
    int16_t old_left_x = simple_left_scroll.virtualX;
    int16_t old_right_x = simple_right_scroll.virtualX;
    bool old_left_waiting = simple_left_scroll.isWaiting;
    bool old_right_waiting = simple_right_scroll.isWaiting;

    // 🔥 更新位置
    updateSimpleScrollingPosition(&simple_left_scroll);
    updateSimpleScrollingPosition(&simple_right_scroll);

    // 🔥 关键优化：只在位置改变或等待状态改变时才重新渲染（移植自demo(4)）
    if (simple_left_scroll.isScrolling &&
        (simple_left_scroll.virtualX != old_left_x || simple_left_scroll.isWaiting != old_left_waiting)) {
        printf("🔥 LEFT RENDER: pos changed %d->%d or wait changed %d->%d\n",
               old_left_x, simple_left_scroll.virtualX, old_left_waiting, simple_left_scroll.isWaiting);
        renderSimpleScrollingText(&simple_left_scroll, SCORE_REGION_LEFT_TEXT, colors.left_text_color);
    }

    if (simple_right_scroll.isScrolling &&
        (simple_right_scroll.virtualX != old_right_x || simple_right_scroll.isWaiting != old_right_waiting)) {
        printf("🔥 RIGHT RENDER: pos changed %d->%d or wait changed %d->%d\n",
               old_right_x, simple_right_scroll.virtualX, old_right_waiting, simple_right_scroll.isWaiting);
        renderSimpleScrollingText(&simple_right_scroll, SCORE_REGION_RIGHT_TEXT, colors.right_text_color);
    }
}

void clearAllScoreRegions() {
    printf("Clearing all score regions...\n");

    for (int i = 0; i < SCORE_REGION_COUNT; i++) {
        clearScoreRegion((ScoreRegion)i);
    }
}

// ==================== 调试函数 ====================
void printScoreModeStatus() {
    printf("=== Score Mode Status (Unified System) ===\n");
    printf("Active: %s\n", score_mode_active ? "YES" : "NO");
    printf("Left Score: %d\n", left_score);
    printf("Right Score: %d\n", right_score);
    printf("Left Text: %d chars, VirtualX: %d, Scrolling: %s, Waiting: %s\n",
           simple_left_scroll.char_count, simple_left_scroll.virtualX,
           simple_left_scroll.isScrolling ? "YES" : "NO",
           simple_left_scroll.isWaiting ? "YES" : "NO");
    printf("Right Text: %d chars, VirtualX: %d, Scrolling: %s, Waiting: %s\n",
           simple_right_scroll.char_count, simple_right_scroll.virtualX,
           simple_right_scroll.isScrolling ? "YES" : "NO",
           simple_right_scroll.isWaiting ? "YES" : "NO");
    printf("Scroll Speed: %dms/pixel\n", simple_left_scroll.moveSpeed);
    printf("Colon Blinking: %s, Visible: %s\n",
           colon_blinking ? "YES" : "NO",
           colon_visible ? "YES" : "NO");
    printf("Colors - LT:0x%04X RT:0x%04X LS:0x%04X RS:0x%04X C:0x%04X\n",
           colors.left_text_color, colors.right_text_color,
           colors.left_score_color, colors.right_score_color,
           colors.colon_color);
    printf("========================================\n");
}

// ==================== 内部辅助函数实现 ====================
static void clearScoreRegion(ScoreRegion region) {
    if (region >= SCORE_REGION_COUNT) return;

    const ScoreRegionInfo* info = &score_regions[region];

    for (int16_t y = info->y1; y <= info->y2; y++) {
        for (int16_t x = info->x1; x <= info->x2; x++) {
            // 直接调用外部显示驱动函数
            extern MatrixPanel_I2S_DMA* dma_display;
            dma_display->drawPixel(x, y, COLOR_BLACK);
        }
    }
}

static void drawScoreDigits(ScoreRegion region, uint16_t score, uint16_t color) {
    if (region >= SCORE_REGION_COUNT) return;

    // 清除区域
    clearScoreRegion(region);

    printf("Drawing score %d in region %d\n", score, region);

    // 分解分数为百位、十位、个位
    uint8_t hundreds = score / 100;
    uint8_t tens = (score % 100) / 10;
    uint8_t ones = score % 10;

    printf("Digits: hundreds=%d, tens=%d, ones=%d\n", hundreds, tens, ones);

    // 修正显示逻辑：严格按照要求不显示前导零
    if (score == 0) {
        // 特殊情况：只显示个位的0
        drawDigitToRegion(region, 0, 16, color);  // 最右边位置
        printf("Displaying single zero\n");
    } else if (score < 10) {
        // 1-9：只显示个位，不显示十位和百位的0
        drawDigitToRegion(region, ones, 16, color);
        printf("Displaying single digit: %d\n", ones);
    } else if (score < 100) {
        // 10-99：显示十位和个位，不显示百位的0
        drawDigitToRegion(region, tens, 8, color);
        drawDigitToRegion(region, ones, 16, color);
        printf("Displaying two digits: %d%d\n", tens, ones);
    } else {
        // 100-999：显示百位、十位、个位
        drawDigitToRegion(region, hundreds, 0, color);
        drawDigitToRegion(region, tens, 8, color);
        drawDigitToRegion(region, ones, 16, color);
        printf("Displaying three digits: %d%d%d\n", hundreds, tens, ones);
    }
}

static void drawDigitToRegion(ScoreRegion region, uint8_t digit, int16_t x_offset, uint16_t color) {
    if (region >= SCORE_REGION_COUNT || digit > 9) return;  // 只接受0-9的数字

    const ScoreRegionInfo* info = &score_regions[region];

    // 修正索引映射：font_data[0]=冒号, font_data[1]=数字0, font_data[2]=数字1, ..., font_data[10]=数字9
    int font_index = digit + 1;  // 数字0-9对应索引1-10

    printf("Drawing digit %d using font_index %d at offset %d\n", digit, font_index, x_offset);

    for (int row = 0; row < 16; row++) {
        uint8_t line_data = font_data[font_index][row];

        for (int col = 0; col < 8; col++) {
            int16_t pixel_x = info->x1 + x_offset + col;
            int16_t pixel_y = info->y1 + row;

            // 边界检查
            if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
                pixel_y >= info->y1 && pixel_y <= info->y2) {

                if (line_data & (0x01 << col)) {
                    extern MatrixPanel_I2S_DMA* dma_display;
                    dma_display->drawPixel(pixel_x, pixel_y, color);
                }
            }
        }
    }
}

static void drawCustomText(ScoreRegion region, uint8_t* text_data, uint8_t char_width,
                          uint8_t char_count, int16_t offset_x, uint16_t color) {
    if (region >= SCORE_REGION_COUNT || !text_data) return;

    const ScoreRegionInfo* info = &score_regions[region];
    int16_t current_x = offset_x;

    for (uint8_t char_idx = 0; char_idx < char_count; char_idx++) {
        // 计算字符数据的起始位置
        uint8_t* char_data = text_data + (char_idx * char_width * SCORE_REGION_HEIGHT);

        // 绘制单个字符
        for (int row = 0; row < SCORE_REGION_HEIGHT; row++) {
            for (int col = 0; col < char_width; col++) {
                int16_t pixel_x = info->x1 + current_x + col;
                int16_t pixel_y = info->y1 + row;

                // 边界检查
                if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
                    pixel_y >= info->y1 && pixel_y <= info->y2) {

                    // 计算字符数据中的位位置
                    int data_index = row * char_width + col;
                    int byte_index = data_index / 8;
                    int bit_index = data_index % 8;

                    if (char_data[byte_index] & (0x01 << bit_index)) {
                        extern MatrixPanel_I2S_DMA* dma_display;
                        dma_display->drawPixel(pixel_x, pixel_y, color);
                    }
                }
            }
        }

        current_x += char_width;

        // 如果超出区域边界，停止绘制
        if (current_x >= SCORE_REGION_WIDTH) break;
    }
}

static void draw16x32Colon(uint16_t color) {
    const ScoreRegionInfo* info = &score_regions[SCORE_REGION_COLON];

    printf("Drawing 16x32 colon in region (%d,%d) to (%d,%d)\n",
           info->x1, info->y1, info->x2, info->y2);

    // 冒号区域是24-39，宽度16像素，高度32像素
    int16_t start_x = info->x1;
    int16_t start_y = info->y1;

    // 先尝试按32行×16列解析（每行2字节）
    for (int row = 0; row < 32 && row < (info->y2 - info->y1 + 1); row++) {
        // 每行2个字节，16个像素
        for (int byte_in_row = 0; byte_in_row < 2; byte_in_row++) {
            uint8_t byte_data = dot_16x32_char[row * 2 + byte_in_row];

            // 处理每个字节中的8个像素
            for (int bit = 0; bit < 8; bit++) {
                int16_t pixel_x = start_x + byte_in_row * 8 + bit;
                int16_t pixel_y = start_y + row;

                // 边界检查
                if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
                    pixel_y >= info->y1 && pixel_y <= info->y2) {

                    // 尝试不同的位序
                    if (byte_data & (0x01 << bit)) {  // 从低位到高位
                        extern MatrixPanel_I2S_DMA* dma_display;
                        dma_display->drawPixel(pixel_x, pixel_y, color);
                    }
                }
            }
        }
    }

    printf("Colon drawing completed\n");
}



// 🔥 绘制单个中文字符
static void drawSingleChineseChar(ScoreRegion region, uint8_t char_idx, int16_t virtualX, uint16_t color) {
    if (region >= SCORE_REGION_COUNT) return;

    const ScoreRegionInfo* info = &score_regions[region];
    const unsigned char (*chars)[32];

    // 🔥 新的动态缓冲区优先逻辑
    DynamicTextBuffer* buffer = getTextBuffer(region);
    const unsigned char* char_data = nullptr;

    if (buffer && buffer->is_active && char_idx < buffer->char_count) {
        // 优先使用动态缓冲区数据
        char_data = buffer->char_data[char_idx];
        // printf("%s: Using dynamic buffer[%d], virtualX=%d\n",
        //        (region == SCORE_REGION_LEFT_TEXT) ? "LEFT" : "RIGHT", char_idx, virtualX);
    } 

    // 如果没有找到有效的字符数据，返回
    if (!char_data) {
        return;
    }

    // 🔥 添加区域坐标调试
    // printf("Region %d coords: (%d,%d) to (%d,%d)\n",
    //        region, info->x1, info->y1, info->x2, info->y2);

    // 🔥 添加字符数据调试
    // printf("Char[%d] data check: first_byte=0x%02X, last_byte=0x%02X\n",
    //        char_idx, chars[char_idx][0], chars[char_idx][23]);

    int pixels_drawn = 0;
    int pixels_skipped = 0;

    // 绘制16×16字符（使用新的char_data）
    for (int row = 0; row < 16; row++) {
       // uint16_t row_data = (char_data[row * 2 + 1] << 8) | char_data[row * 2];

        for (int col = 0; col < 16; col++) {
            int16_t pixel_x = info->x1 + virtualX + col;
            int16_t pixel_y = info->y1 + row;
            bool pixel_on;
            // 🔥 智能边界检查：只绘制在区域内的像素
            if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
                pixel_y >= info->y1 && pixel_y <= info->y2) {

                 if (col < 8) {
                    // 前8列：检查第一个字节的对应位
                    pixel_on = char_data[row * 2] & (0x80 >> col);
                } else {
                    // 后8列：检查第二个字节的对应位
                    pixel_on = char_data[row * 2 + 1] & (0x80 >> (col - 8));
                }
                
                if (pixel_on) {
                    extern MatrixPanel_I2S_DMA* dma_display;
                    dma_display->drawPixel(pixel_x, pixel_y, color);
                    pixels_drawn++;
                }
            } else {
                pixels_skipped++;
            }
        }
    }

    // 🔥 添加绘制结果调试
    // printf("Char[%d] render: pixels_drawn=%d, pixels_skipped=%d\n",
    //        char_idx, pixels_drawn, pixels_skipped);
}



// 🔥 统一架构：简化的位置更新函数（移植自demo(4)）
static void updateSimpleScrollingPosition(SimpleScrollInfo* scroll_info) {
    if (!scroll_info->isScrolling) {
        // printf("🔥 DEBUG: Not scrolling, skipping update\n");
        return;
    }

    // 🔥 静态显示优化：如果moveSpeed为0，表示静态显示，不移动
    if (scroll_info->moveSpeed == 0) {
        // printf("🔥 DEBUG: Static mode (moveSpeed=0), skipping movement\n");
        return;
    }

    uint32_t currentTime = millis();

    // 🔥 等待重置状态处理（滚出后等待1秒）
    if (scroll_info->isWaiting) {
        if (currentTime - scroll_info->waitStartTime >= 1000) {  // 等待1秒
            scroll_info->isWaiting = false;
            scroll_info->virtualX = SCORE_REGION_WIDTH;  // 重置到右边界外
            scroll_info->lastMoveTime = currentTime;
            printf("🔥 SCROLL RESET: virtualX=%d\n", scroll_info->virtualX);
        }
        return;  // 等待期间不更新位置
    }

    // 🔥 流畅滚动：每次调用都检查是否需要移动（移植自demo(4)）
    if (currentTime - scroll_info->lastMoveTime >= scroll_info->moveSpeed) {
        int16_t old_x = scroll_info->virtualX;
        scroll_info->virtualX -= 1;  // 向左移动1像素
        scroll_info->lastMoveTime = currentTime;
        printf("🔥 SCROLL MOVE: %d -> %d (speed=%dms)\n", old_x, scroll_info->virtualX, scroll_info->moveSpeed);

        // 🔥 检查是否完全移出屏幕（进入等待状态）
        if (scroll_info->virtualX + scroll_info->totalWidth < 0) {
            scroll_info->isWaiting = true;
            scroll_info->waitStartTime = currentTime;
            printf("🔥 SCROLL COMPLETE: entering 1-second wait\n");
        }
    }
}

// 🔥 统一架构：优化的渲染函数（移植自demo(4)，减少闪烁）
static void renderSimpleScrollingText(SimpleScrollInfo* scroll_info, ScoreRegion region, uint16_t color) {
    if (!scroll_info->isScrolling) return;

    const ScoreRegionInfo* info = &score_regions[region];
    extern MatrixPanel_I2S_DMA* dma_display;

    // 🔥 关键优化：一次性填充背景，减少闪烁（移植自demo(4)）
    uint16_t bgColor = COLOR_BLACK;  // 可配置的背景色
    dma_display->fillRect(info->x1, info->y1,
                         info->x2 - info->x1 + 1, info->y2 - info->y1 + 1,
                         bgColor);

    // 🔥 等待状态时不绘制字符（实现1秒空白）
    if (scroll_info->isWaiting) return;

    // 🔥 智能绘制：只绘制可见的字符（移植自demo(4)）
    int16_t currentX = scroll_info->virtualX;

    for (uint8_t char_idx = 0; char_idx < scroll_info->char_count; char_idx++) {
        // 计算实际屏幕坐标
        int16_t screen_x = info->x1 + currentX;

        // 优化的可见性检查
        if (screen_x + 16 > info->x1 && screen_x < info->x2) {
            // 字符有部分可见，进行绘制
            drawSingleChineseChar(region, char_idx, currentX, color);
        }

        currentX += 16;  // 移动到下一个字符

        // 如果完全超出右边界，停止绘制
        if (screen_x > info->x2) break;
    }
}





// ==================== 测试函数 ====================
// void testScoreMode() {
//     printf("\n=== Starting Score Mode Test ===\n");

//     // 1. 初始化和进入计分模式
//     printf("Step 1: Initializing Score Mode...\n");
//     if (!initScoreMode()) {
//         printf("ERROR: Failed to initialize Score Mode\n");
//         return;
//     }

//     enterScoreMode();
//     delay(2000);

//     // 2. 测试分数显示
//     printf("Step 2: Testing score display...\n");
//     for (int i = 0; i <= 10; i++) {
//         setScoreLeftValue(i);
//         setScoreRightValue(i * 10);
//         delay(500);
//     }

//     // 3. 测试分数递增
//     printf("Step 3: Testing score increment...\n");
//     for (int i = 0; i < 20; i++) {
//         incrementScoreLeft();
//         incrementScoreRight();
//         delay(300);
//     }

//     // 4. 测试颜色变化
//     printf("Step 4: Testing color changes...\n");
//     setScoreLeftCounterColor(COLOR_RED);
//     delay(1000);
//     setScoreRightCounterColor(COLOR_BLUE);
//     delay(1000);
//     setScoreColonColor(COLOR_GREEN);
//     delay(1000);

//     // 5. 测试冒号闪烁
//     printf("Step 5: Testing colon blinking...\n");
//     setScoreColonBlinking(true);
//     delay(5000);
//     setScoreColonBlinking(false);

//     // 6. 测试大数值和溢出
//     printf("Step 6: Testing large values and overflow...\n");
//     setScoreLeftValue(995);
//     setScoreRightValue(998);
//     delay(1000);

//     for (int i = 0; i < 10; i++) {
//         incrementScoreLeft();
//         incrementScoreRight();
//         delay(500);
//     }

//     // 7. 测试中文字符显示
//     printf("Step 7: Testing Chinese character display...\n");

//     // 测试左侧中文字符（"机电队"）- 使用新系统
//     printf("Testing left Chinese text: 机电队 (New System)\n");
//     setScoreLeftChineseText(3);  // 使用新的滚动系统
//     delay(3000);

//     // 测试右侧中文字符（"物电队"）- 使用新系统
//     printf("Testing right Chinese text: 物电队 (New System)\n");
//     setScoreRightChineseText(3);  // 使用新的滚动系统
//     delay(3000);

//     // 清除文字区域
//     printf("Clearing text regions...\n");
//     clearScoreRegion(SCORE_REGION_LEFT_TEXT);
//     clearScoreRegion(SCORE_REGION_RIGHT_TEXT);
//     delay(2000);

//     // 8. 重置测试
//     printf("Step 8: Testing reset functions...\n");
//     delay(1000);
//     resetAllScores();
//     delay(2000);

//     // 9. 打印最终状态
//     printf("Step 9: Final status check...\n");
//     printScoreModeStatus();

//     printf("=== Score Mode Test Completed ===\n\n");
// }




// ==================== 智能文本显示辅助函数 ====================

// 创建模拟蓝牙文本数据包（新格式：包头+命令+区域选择+语言+颜色+文本数据+包尾）
uint16_t createMockTextPacket(uint8_t* buffer, uint8_t region_select, uint8_t char_count,
                             uint8_t language, uint16_t color, const uint8_t* char_data) {
    uint16_t index = 0;

    // 包头
    buffer[index++] = 0xAA;
    buffer[index++] = 0x55;

    // 命令
    buffer[index++] = BT_CMD_SET_TEXT;

    // 数据长度（高字节+低字节）
    uint16_t data_len = 4 + (char_count * 24); // 区域1字节+语言1字节+颜色2字节+文本数据
    buffer[index++] = (data_len >> 8) & 0xFF;
    buffer[index++] = data_len & 0xFF;

    // 文本左右区域选择（新增）
    buffer[index++] = region_select; // 0=左侧, 1=右侧

    // 字体语言选择
    buffer[index++] = language; // 0=中文, 1=英文

    // 字体颜色字节（RGB565，高字节+低字节）
    buffer[index++] = (color >> 8) & 0xFF;
    buffer[index++] = color & 0xFF;

    // 文本字节数据（12x12点阵，每个字符24字节）
    for (uint8_t i = 0; i < char_count; i++) {
        for (uint8_t j = 0; j < 24; j++) {
            buffer[index++] = char_data[i * 24 + j];
        }
    }

    // 包尾
    buffer[index++] = 0x0D;
    buffer[index++] = 0x0A;

    printf("Created mock packet: region=%d, chars=%d, lang=%d, color=0x%04X, total_len=%d\n",
           region_select, char_count, language, color, index);

    return index; // 返回总长度
}

// 获取模拟中文字符数据（区分左右区域数据源）
const uint8_t* getMockChineseDataForRegion(ScoreRegion region, uint8_t char_count) {
    static uint8_t mock_data[10 * 24]; // 最多10个字符

    const char* region_name = (region == SCORE_REGION_LEFT_TEXT) ? "LEFT" : "RIGHT";
    printf("Generating mock data for %s region (%d chars)\n", region_name, char_count);

    if (region == SCORE_REGION_LEFT_TEXT) {
        // 左区域：使用left_12x12_chars数组
        // 期望效果：机、机电、机电队、机电队工、机电队工程、机电队工程学
        uint8_t available_chars = 7;  // left_12x12_chars有7个字符

        for (uint8_t i = 0; i < char_count && i < 10; i++) {
            if (i < available_chars) {
                memcpy(&mock_data[i * 24], left_12x12_chars[i], 24);
                printf("LEFT[%d]: Using left_12x12_chars[%d]\n", i, i);
            } else {
                // 超出范围时填充空白
                memset(&mock_data[i * 24], 0, 24);
                printf("LEFT[%d]: Using blank data\n", i);
            }
        }
    } else if (region == SCORE_REGION_RIGHT_TEXT) {
        // 右区域：使用right_12x12_chars数组
        // 期望效果：物、物电、物电队、物电队理、物电队理子、物电队理子系
        uint8_t available_chars = 6;  // right_12x12_chars有6个字符

        for (uint8_t i = 0; i < char_count && i < 10; i++) {
            if (i < available_chars) {
                memcpy(&mock_data[i * 24], right_12x12_chars[i], 24);
                printf("RIGHT[%d]: Using right_12x12_chars[%d]\n", i, i);
            } else {
                // 超出范围时填充空白
                memset(&mock_data[i * 24], 0, 24);
                printf("RIGHT[%d]: Using blank data\n", i);
            }
        }
    }

    return mock_data;
}

// 处理智能文本命令（新格式解析）
void handleSmartTextCommand(uint8_t* data, uint16_t length) {
    printf("=== Processing Smart Text Command ===\n");
    printf("Data length: %d bytes\n", length);

    // 验证包头
    if (length < 10 || data[0] != 0xAA || data[1] != 0x55) {
        printf("Error: Invalid packet header or length\n");
        return;
    }

    // 解析数据包
    uint8_t command = data[2];
    uint16_t data_len = (data[3] << 8) | data[4];
    uint8_t region_select = data[5];    // 新增：区域选择
    uint8_t language = data[6];         // 语言选择
    uint16_t color = (data[7] << 8) | data[8]; // 字体颜色

    // 验证包尾
    if (data[length-2] != 0x0D || data[length-1] != 0x0A) {
        printf("Error: Invalid packet tail\n");
        return;
    }

    // 计算字符数量
    uint8_t char_count = (data_len - 4) / 32; // 减去区域+语言+颜色字节

    printf("Command: 0x%02X, Region: %d, Language: %d, Color: 0x%04X, Chars: %d\n",
           command, region_select, language, color, char_count);

    // 提取文本数据
    uint8_t* text_data = &data[9]; // 从第9字节开始是文本数据

    // 根据区域选择确定目标区域
    ScoreRegion target_region;
    if (region_select == 0) {
        target_region = SCORE_REGION_LEFT_TEXT;
        printf("Target: LEFT text region\n");
    } else if (region_select == 1) {
        target_region = SCORE_REGION_RIGHT_TEXT;
        printf("Target: RIGHT text region\n");
    } else {
        printf("Error: Invalid region selection: %d\n", region_select);
        return;
    }

    // 应用智能显示逻辑
    applySmartTextDisplay(text_data, char_count, color, language, target_region);
}

// 应用智能文本显示
void applySmartTextDisplay(uint8_t* char_data, uint8_t char_count,
                          uint16_t color, uint8_t language, ScoreRegion region) {
    // 获取区域信息
    const ScoreRegionInfo* region_info = &score_regions[region];
    uint8_t region_width = region_info->x2 - region_info->x1 + 1;
    uint8_t text_width = char_count * 16; // 每个中文字符16像素宽

    printf("=== Smart Display Logic ===\n");
    printf("Region width: %d pixels, Text width: %d pixels\n", region_width, text_width);

    // 智能判断显示方式
    if (text_width <= region_width) {
        printf("Using STATIC display (text fits in region)\n");
        setupStaticTextDisplay(char_data, char_count, color, region);
    } else {
        printf("Using SCROLLING display (text exceeds region)\n");
        setupScrollingTextDisplay(char_data, char_count, color, region);
    }
}



// 设置滚动文本显示（使用新的动态缓冲区系统）
void setupScrollingTextDisplay(uint8_t* char_data, uint8_t char_count, uint16_t color, ScoreRegion region) {
    printf("🔄 Setting up scrolling text display: %d chars for region %d\n", char_count, region);
    printf("   Expected scroll: %s (chars > 2)\n", (char_count > 2) ? "YES" : "NO");

    // 1. 加载数据到动态缓冲区
    loadTextBuffer(region, char_data, char_count, color);
    printf("   ✅ Text buffer loaded with %d characters\n", char_count);

    // 2. 使用原有的优秀滚动逻辑
    if (region == SCORE_REGION_LEFT_TEXT) {
        printf("   🎯 Setting up LEFT text scrolling\n");
        setScoreLeftTextColor(color);         // 使用正确的颜色设置函数
        setScoreLeftChineseText(char_count);  // 保留原有函数调用
        printf("   📊 Left scroll state: isScrolling=%s\n",
               simple_left_scroll.isScrolling ? "TRUE" : "FALSE");
    } else if (region == SCORE_REGION_RIGHT_TEXT) {
        printf("   🎯 Setting up RIGHT text scrolling\n");
        setScoreRightTextColor(color);        // 使用正确的颜色设置函数
        setScoreRightChineseText(char_count); // 保留原有函数调用
        printf("   📊 Right scroll state: isScrolling=%s\n",
               simple_right_scroll.isScrolling ? "TRUE" : "FALSE");
    }

    printf("✅ Scrolling display setup completed\n");
}

// 设置静态文本显示（使用新的动态缓冲区系统）
void setupStaticTextDisplay(uint8_t* char_data, uint8_t char_count, uint16_t color, ScoreRegion region) {
    printf("Setting up static text display: %d chars\n", char_count);

    // 1. 加载数据到动态缓冲区
    loadTextBuffer(region, char_data, char_count, color);

    // 2. 清除区域
    clearScoreRegion(region);

    // 3. 停止滚动（直接操作滚动状态，保持与原有逻辑一致）
    if (region == SCORE_REGION_LEFT_TEXT) {
        simple_left_scroll.isScrolling = false;
    } else if (region == SCORE_REGION_RIGHT_TEXT) {
        simple_right_scroll.isScrolling = false;
    }

    // 4. 计算居中位置并渲染
    const ScoreRegionInfo* region_info = &score_regions[region];
    uint8_t region_width = region_info->x2 - region_info->x1 + 1;
    uint8_t text_width = char_count * 16;
    int16_t center_x = (region_width - text_width) / 2;
    if (center_x < 0) center_x = 0;

    printf("Static display: centered at x=%d\n", center_x);

    // 5. 渲染静态文本
    renderStaticSmartText(region, char_data, char_count, center_x, color);
}

// 渲染静态智能文本
void renderStaticSmartText(ScoreRegion region, uint8_t* char_data, uint8_t char_count,
                          int16_t offset_x, uint16_t color) {
    const ScoreRegionInfo* info = &score_regions[region];
    extern MatrixPanel_I2S_DMA* dma_display;

    printf("Rendering static smart text: %d chars at offset %d\n", char_count, offset_x);

    // 绘制每个字符
    int16_t currentX = offset_x;
    for (uint8_t char_idx = 0; char_idx < char_count; char_idx++) {
        // 绘制单个字符
        drawSmartChineseChar(region, char_idx, currentX, color);
        currentX += 16; // 移动到下一个字符位置

        // 边界检查
        if (info->x1 + currentX > info->x2) break;
    }

    printf("Static text rendering completed\n");
}

// 绘制智能中文字符
void drawSmartChineseChar(ScoreRegion region, uint8_t char_idx, int16_t virtualX, uint16_t color) {
    if (region >= SCORE_REGION_COUNT) return;

    const ScoreRegionInfo* info = &score_regions[region];

    // 从动态缓冲区读取数据
    DynamicTextBuffer* buffer = getTextBuffer(region);
    const unsigned char* char_data = nullptr;

    if (buffer && buffer->is_active && char_idx < buffer->char_count) {
        char_data = buffer->char_data[char_idx];
    }

    // 如果没有找到有效的字符数据，返回
    if (!char_data) {
        return;
    }

    
    
    // 绘制16×16字符
    for (int row = 0; row < 16; row++) {
        for (int col = 0; col < 16; col++) {
            int16_t pixel_x = info->x1 + virtualX + col;
            int16_t pixel_y = info->y1 + row;
            bool pixel_on;
            // 边界检查
            if (pixel_x >= info->x1 && pixel_x <= info->x2 &&
                pixel_y >= info->y1 && pixel_y <= info->y2) {

                if (col < 8) {
                    // 前8列：检查第一个字节的对应位
                    pixel_on = char_data[row * 2] & (0x80 >> col);
                } else {
                    // 后8列：检查第二个字节的对应位
                    pixel_on = char_data[row * 2 + 1] & (0x80 >> (col - 8));
                }

                if (pixel_on) {
                    extern MatrixPanel_I2S_DMA* dma_display;
                    dma_display->drawPixel(pixel_x, pixel_y, color);
                }
            }
        }
    }
}

// 清除智能文本显示（使用新的缓冲区系统）
void clearSmartTextDisplay(ScoreRegion region) {
    printf("Clearing smart text display for region %d\n", region);
    clearScoreRegion(region);

    // 清除动态缓冲区
    clearTextBuffer(region);
}

// ==================== 蓝牙控制接口实现 ====================

// 处理蓝牙计分模式文本命令 - 支持RGB颜色
void handleBluetoothScoreModeText(uint8_t region, uint8_t colorR, uint8_t colorG, uint8_t colorB,
                                 const uint8_t* textData, uint16_t textDataLength) {
    printf("Bluetooth Score Mode Text Command Received:\n");
    printf("  Region: %d\n", region);
    printf("  Color: RGB(%d,%d,%d)\n", colorR, colorG, colorB);
    printf("  Text Data Length: %d bytes\n", textDataLength);

    // 参数验证 - 严格按照协议要求的十六进制范围
    if (region != BT_SCORE_REGION_LEFT_TEXT && region != BT_SCORE_REGION_RIGHT_TEXT) {
        printf("Error: Invalid text region 0x%02X (only 0x00 and 0x02 supported)\n", region);
        return;
    }

    if (textData == nullptr) {
        printf("Error: Text data is null\n");
        return;
    }

    // 验证数据长度范围
    if (textDataLength < BT_SCORE_CHAR_BYTES) {
        printf("Error: Text data too short %d bytes (minimum %d bytes for 1 char)\n",
               textDataLength, BT_SCORE_CHAR_BYTES);
        return;
    }

    if (textDataLength > BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES) {
        printf("Error: Text data too long %d bytes (maximum %d bytes for %d chars)\n",
               textDataLength, BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES, BT_SCORE_MAX_CHARS);
        return;
    }

    // 🔥 移除重复的模式切换：由上层函数保证已在计分模式
    // enterScoreMode();  // 删除：避免重复清屏和重绘所有区域

    // 确保在计分模式中（由上层函数保证）
    if (!score_mode_active) {
        printf("Error: Not in score mode, command ignored\n");
        return;
    }

    // 转换区域编号到内部枚举
    ScoreRegion targetRegion;
    if (region == BT_SCORE_REGION_LEFT_TEXT) {
        targetRegion = SCORE_REGION_LEFT_TEXT;
        printf("  Target: Left text region\n");
    } else {
        targetRegion = SCORE_REGION_RIGHT_TEXT;
        printf("  Target: Right text region\n");
    }

    // 计算字符数量（每个16×16字符需要32字节）
    uint8_t charCount = textDataLength / BT_SCORE_CHAR_BYTES;
    printf("  Character Count: %d\n", charCount);

    // 复制文本数据到本地缓冲区
    uint8_t localTextData[BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES];
    memcpy(localTextData, textData, textDataLength);

    // 转换RGB888到RGB565颜色格式
    uint16_t textColor = ((colorR & 0xF8) << 8) | ((colorG & 0xFC) << 3) | (colorB >> 3);

    // 应用智能文本显示 - 使用解析出的颜色
    applySmartTextDisplay(localTextData, charCount, textColor, 0, targetRegion);

    printf("Bluetooth Score Mode Text setup completed successfully\n");
    printf("  Region width: 24 pixels, Character count: %d\n", charCount);
    printf("  Auto-scroll: %s\n", (charCount * 16 > 24) ? "Enabled" : "Disabled");

    // 🔥 调试：打印最终的滚动状态
    if (targetRegion == SCORE_REGION_LEFT_TEXT) {
        printf("  📊 Final LEFT scroll state: isScrolling=%s, moveSpeed=%d, totalWidth=%d\n",
               simple_left_scroll.isScrolling ? "TRUE" : "FALSE",
               simple_left_scroll.moveSpeed, simple_left_scroll.totalWidth);
    } else {
        printf("  📊 Final RIGHT scroll state: isScrolling=%s, moveSpeed=%d, totalWidth=%d\n",
               simple_right_scroll.isScrolling ? "TRUE" : "FALSE",
               simple_right_scroll.moveSpeed, simple_right_scroll.totalWidth);
    }
}

// 🔧 调试函数：测试蓝牙文本滚动
void testBluetoothTextScrolling() {
    printf("\n=== Testing Bluetooth Text Scrolling ===\n");

    // 确保计分模式激活
    if (!score_mode_active) {
        initScoreMode();
        enterScoreMode();
    }

    // 创建测试数据：5个字符（应该触发滚动）
    uint8_t testData[5 * 32];  // 5个字符，每个24字节
    memset(testData, 0xFF, sizeof(testData));  // 填充测试数据

    printf("🧪 Testing LEFT region with 5 characters (should scroll)\n");
    handleBluetoothScoreModeText(BT_SCORE_REGION_LEFT_TEXT, 255, 255, 255, testData, sizeof(testData));

    delay(2000);

    printf("🧪 Testing RIGHT region with 5 characters (should scroll)\n");
    handleBluetoothScoreModeText(BT_SCORE_REGION_RIGHT_TEXT, 255, 255, 255, testData, sizeof(testData));

    printf("=== Bluetooth Text Scrolling Test Complete ===\n");
}

// 处理蓝牙计分模式数字命令
void handleBluetoothScoreModeNumber(uint8_t region, uint8_t colorR, uint8_t colorG, uint8_t colorB,
                                   uint16_t initValue, uint8_t operation, uint16_t operValue) {
    printf("Bluetooth Score Mode Number Command Received:\n");
    printf("  Region: %d\n", region);
    printf("  Color: RGB(%d,%d,%d)\n", colorR, colorG, colorB);
    printf("  Initial Value: %d\n", initValue);
    printf("  Operation: %s %d\n", (operation == 0) ? "Add" : "Subtract", operValue);

    // 参数验证 - 严格按照协议要求的十六进制范围
    if (region != BT_SCORE_REGION_LEFT_SCORE && region != BT_SCORE_REGION_RIGHT_SCORE) {
        printf("Error: Invalid score region 0x%02X (only 0x01 and 0x03 supported)\n", region);
        return;
    }

    if (initValue > 0x03E7) {  // 0x03E7 = 999
        printf("Error: Invalid initial value 0x%04X (0x0000~0x03E7 for 0~999)\n", initValue);
        return;
    }

    if (operValue > 0x03E7) {  // 0x03E7 = 999
        printf("Error: Invalid operation value 0x%04X (0x0000~0x03E7 for 0~999)\n", operValue);
        return;
    }

    if (operation > 0x01) {  // 0x01 = 1
        printf("Error: Invalid operation 0x%02X (0x00=add, 0x01=subtract)\n", operation);
        return;
    }

    // 🔥 移除重复的模式切换：由上层函数保证已在计分模式
    // enterScoreMode();  // 删除：避免重复清屏和重绘所有区域

    // 确保在计分模式中（由上层函数保证）
    if (!score_mode_active) {
        printf("Error: Not in score mode, command ignored\n");
        return;
    }

    // 转换RGB888到RGB565颜色格式
    uint16_t color = ((colorR & 0xF8) << 8) | ((colorG & 0xFC) << 3) | (colorB >> 3);

    // 计算最终数值
    uint16_t finalValue;
    if (operation == 0) {  // 加法
        finalValue = initValue + operValue;
        if (finalValue > 999) finalValue = 999;  // 限制最大值
    } else {  // 减法
        if (initValue >= operValue) {
            finalValue = initValue - operValue;
        } else {
            finalValue = 0;  // 避免负数
        }
    }

    // 根据区域设置分数和颜色
    if (region == BT_SCORE_REGION_LEFT_SCORE) {
        printf("  Target: Left score region\n");
        setScoreLeftCounterColor(color);
        setScoreLeftValue(finalValue);
    } else {
        printf("  Target: Right score region\n");
        setScoreRightCounterColor(color);
        setScoreRightValue(finalValue);
    }

    printf("  Final Value: %d\n", finalValue);
    printf("Bluetooth Score Mode Number setup completed successfully\n");
}
