#include "DisplayDriver.h"
#include "LEDController.h" // 包含ScreenParams定义

// 🔧 坐标变换函数：将文字坐标变换到实际屏幕坐标
void drawTextPixel(int x, int y, uint16_t color)
{
    // 应用坐标变换和裁剪
    int newX = x + currentScreen.startX;
    int newY = y + currentScreen.startY;

    // 裁剪检查：确保不超出文字区域边界
    if (newX < currentScreen.startX || newX >= currentScreen.startX + currentScreen.width ||
        newY < currentScreen.startY || newY >= currentScreen.startY + currentScreen.height)
    {
        return; // 超出文字区域，不绘制
    }

    // 最终边界检查：确保不超出物理屏幕
    if (newX >= 0 && newX < SCREEN_WIDTH && newY >= 0 && newY < SCREEN_HEIGHT)
    {
        dma_display->drawPixel(newX, newY, color);
    }
}

// 外部函数声明
extern uint16_t getSpecificCharColor(int charIndex, bool isUpper);
extern bool hasSpecificCharColor(int charIndex, bool isUpper);
extern uint16_t getRandomColor(int charIndex, bool isUpper);

// 渐变色组合结构
struct GradientColors
{
    uint8_t colors[7][3];
};

// 预定义的渐变色组合
const GradientColors gradientCombinations[6] = {
    // 上下渐变组合1：红橙黄绿青蓝紫
    {{{255, 0, 0}, {255, 127, 0}, {255, 255, 0}, {0, 255, 0}, {0, 255, 255}, {0, 0, 255}, {127, 0, 255}}},
    // 上下渐变组合2：明亮彩虹色（红粉紫蓝青绿黄）
    {{{255, 0, 0}, {255, 0, 255}, {128, 0, 255}, {0, 128, 255}, {0, 255, 255}, {0, 255, 0}, {255, 255, 0}}},
    // 上下渐变组合3：紫蓝青绿黄橙红
    {{{127, 0, 255}, {0, 0, 255}, {0, 255, 255}, {0, 255, 0}, {255, 255, 0}, {255, 127, 0}, {255, 0, 0}}},
    // 左右渐变组合1：火焰渐变（红→橙→黄→白→黄→橙→红）
    {{{255, 0, 0}, {255, 127, 0}, {255, 255, 0}, {255, 255, 255}, {255, 255, 0}, {255, 127, 0}, {255, 0, 0}}},
    // 左右渐变组合2：明亮霓虹色（粉红→橙→黄→绿→青→蓝→紫）
    {{{255, 20, 147}, {255, 165, 0}, {255, 255, 0}, {0, 255, 0}, {0, 255, 255}, {0, 0, 255}, {138, 43, 226}}},
    // 左右渐变组合3：森林渐变（深绿→浅绿→黄绿→白→黄绿→浅绿→深绿）
    {{{0, 100, 0}, {0, 200, 0}, {127, 255, 0}, {255, 255, 255}, {127, 255, 0}, {0, 200, 0}, {0, 100, 0}}}};

void drawChar16x16(int x, int y, const uint16_t *font_data, uint16_t color)
{
    // 使用列取模方式：每个uint16_t代表一列的16个像素
    for (int col = 0; col < 16; col++)
    {
        uint16_t col_data = font_data[col];
        for (int row = 0; row < 16; row++)
        {
            // 检查对应位是否为1（从高位开始，高位对应上方像素）
            if (col_data & (0x8000 >> row))
            {
                int px = x + col;
                int py = y + row;
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 竖向显示字符（向左旋转90度）
void drawChar16x16Vertical(int x, int y, const uint16_t *font_data, uint16_t color)
{
    // 向左旋转90度：原来的(col, row)变成(row, 15-col)
    for (int col = 0; col < 16; col++)
    {
        uint16_t col_data = font_data[col];
        for (int row = 0; row < 16; row++)
        {
            // 检查对应位是否为1
            if (col_data & (0x8000 >> row))
            {
                int px = x + row;        // 向左旋转后的X坐标
                int py = y + (15 - col); // 向左旋转后的Y坐标
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

void drawChar16x16Gradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode)
{
    // 使用列取模方式：每个uint16_t代表一列的16个像素
    for (int col = 0; col < 16; col++)
    {
        uint16_t col_data = font_data[col];
        for (int row = 0; row < 16; row++)
        {
            // 检查对应位是否为1（从高位开始，高位对应上方像素）
            if (col_data & (0x8000 >> row))
            {
                int px = x + col;
                int py = y + row;
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    // 根据像素位置获取渐变色
                    uint16_t color = getGradientColor(px, py, isUpper, gradientMode);
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 竖向渐变字符（向左旋转90度）
void drawChar16x16VerticalGradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode)
{
    // 向左旋转90度：原来的(col, row)变成(row, 15-col)
    for (int col = 0; col < 16; col++)
    {
        uint16_t col_data = font_data[col];
        for (int row = 0; row < 16; row++)
        {
            // 检查对应位是否为1
            if (col_data & (0x8000 >> row))
            {
                int px = x + row;        // 向左旋转后的X坐标
                int py = y + (15 - col); // 向左旋转后的Y坐标
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    // 根据旋转后的像素位置获取渐变色
                    uint16_t color = getGradientColor(px, py, isUpper, gradientMode);
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

void drawString16x16(int x, int y, const uint16_t *font_array, int char_count, uint16_t color)
{
    int pos_x = x;

    // 遍历每个字符的字库数据
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];
        drawChar16x16(pos_x, y, char_data, color);
        pos_x += CHAR_SPACING_16; // 移动到下一个字符位置
    }
}

void drawString16x16Gradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode)
{
    int pos_x = x;

    // 遍历每个字符的字库数据
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];
        drawChar16x16Gradient(pos_x, y, char_data, isUpper, gradientMode);
        pos_x += CHAR_SPACING_16; // 移动到下一个字符位置
    }
}

// 竖向显示字符串
void drawString16x16Vertical(int x, int y, const uint16_t *font_array, int char_count, uint16_t color)
{
    int pos_x = x;

    // 遍历每个字符的字库数据，向左旋转后从左到右排列
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];
        drawChar16x16Vertical(pos_x, y, char_data, color);
        pos_x += CHAR_SPACING_16; // 水平移动到下一个字符位置
    }
}

// 竖向渐变字符串
void drawString16x16VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode)
{
    int pos_x = x;

    // 遍历每个字符的字库数据，向左旋转后从左到右排列
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];
        drawChar16x16VerticalGradient(pos_x, y, char_data, isUpper, gradientMode);
        pos_x += CHAR_SPACING_16; // 水平移动到下一个字符位置
    }
}

// RGB888转RGB565颜色格式
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b)
{
    return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

// 获取渐变色
uint16_t getGradientColor(int x, int y, bool isUpper, uint8_t gradientMode)
{
    if (gradientMode == BT_GRADIENT_FIXED)
    {
        // 固定色，返回基础颜色
        return isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
    }

    // 获取对应的渐变组合
    int combinationIndex = -1;
    bool isVertical = false;

    if (gradientMode >= BT_GRADIENT_VERTICAL_1 && gradientMode <= BT_GRADIENT_VERTICAL_3)
    {
        combinationIndex = gradientMode - BT_GRADIENT_VERTICAL_1; // 0,1,2
        isVertical = true;
    }
    else if (gradientMode >= BT_GRADIENT_HORIZONTAL_1 && gradientMode <= BT_GRADIENT_HORIZONTAL_3)
    {
        combinationIndex = gradientMode - BT_GRADIENT_HORIZONTAL_1 + 3; // 3,4,5
        isVertical = false;
    }

    if (combinationIndex < 0 || combinationIndex >= 6)
    {
        // 无效的渐变模式，返回基础颜色
        return isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
    }

    const GradientColors &gradient = gradientCombinations[combinationIndex];
    int colorIndex = 0;

    if (isVertical)
    {
        // 🔧 上下渐变：根据Y坐标计算颜色索引（使用动态屏幕高度）
        int relativeY = y;                          // 使用绝对Y坐标
        int effectiveHeight = currentScreen.height; // 使用动态屏幕高度
        if (!isUpper && currentScreen.height == 32)
        {
            // 16x16双屏模式下的下半屏
            relativeY = y - 16;
            effectiveHeight = 16;
        }
        colorIndex = (relativeY * 7) / effectiveHeight; // 动态高度分成7块
    }
    else
    {
        // 🔧 左右渐变：根据X坐标计算颜色索引（使用动态屏幕宽度）
        colorIndex = (x * 7) / currentScreen.width; // 动态屏幕宽度分成7块
    }

    // 确保索引在有效范围内
    if (colorIndex < 0)
        colorIndex = 0;
    if (colorIndex >= 7)
        colorIndex = 6;

    // 获取对应颜色并转换为RGB565
    uint8_t r = gradient.colors[colorIndex][0];
    uint8_t g = gradient.colors[colorIndex][1];
    uint8_t b = gradient.colors[colorIndex][2];

    return rgb888to565(r, g, b);
}

// 32x32字符显示函数
void drawChar32x32(int x, int y, const uint16_t *font_data, uint16_t color)
{
    // 使用列取模方式：每个uint16_t代表一列的32个像素
    for (int col = 0; col < 32; col++)
    {
        uint16_t column_data_low = font_data[col * 2];      // 低16位
        uint16_t column_data_high = font_data[col * 2 + 1]; // 高16位

        for (int row = 0; row < 32; row++)
        {
            bool pixel_on;
            if (row < 16)
            {
                pixel_on = (column_data_low & (0x8000 >> row)) != 0; // 统一为从高位开始
            }
            else
            {
                pixel_on = (column_data_high & (0x8000 >> (row - 16))) != 0; // 统一为从高位开始
            }

            if (pixel_on)
            {
                int px = x + col;
                int py = y + row;
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 32x32竖向字符显示函数
void drawChar32x32Vertical(int x, int y, const uint16_t *font_data, uint16_t color)
{
    // 竖向显示：将字符向左旋转90度
    for (int col = 0; col < 32; col++)
    {
        uint16_t column_data_low = font_data[col * 2];      // 低16位
        uint16_t column_data_high = font_data[col * 2 + 1]; // 高16位

        for (int row = 0; row < 32; row++)
        {
            bool pixel_on;
            if (row < 16)
            {
                pixel_on = (column_data_low & (0x8000 >> row)) != 0; // 统一为从高位开始
            }
            else
            {
                pixel_on = (column_data_high & (0x8000 >> (row - 16))) != 0; // 统一为从高位开始
            }

            if (pixel_on)
            {
                // 旋转坐标：原(col, row) -> 新(row, 31-col)
                int px = x + row;
                int py = y + (31 - col);
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 32x32字符串显示函数
void drawString32x32(int x, int y, const uint16_t *font_array, int char_count, uint16_t color)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t
        drawChar32x32(x + i * CHAR_SPACING_32, y, char_data, color);
    }
}

// 32x32竖向字符串显示函数
void drawString32x32Vertical(int x, int y, const uint16_t *font_array, int char_count, uint16_t color)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t
        drawChar32x32Vertical(x + i * CHAR_SPACING_32, y, char_data, color);
    }
}

// 32x32字符渐变色显示函数
void drawChar32x32Gradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode)
{
    // 使用列取模方式：每个uint16_t代表一列的32个像素
    for (int col = 0; col < 32; col++)
    {
        uint16_t column_data_low = font_data[col * 2];      // 低16位
        uint16_t column_data_high = font_data[col * 2 + 1]; // 高16位

        for (int row = 0; row < 32; row++)
        {
            bool pixel_on;
            if (row < 16)
            {
                pixel_on = (column_data_low & (0x8000 >> row)) != 0;
            }
            else
            {
                pixel_on = (column_data_high & (0x8000 >> (row - 16))) != 0;
            }

            if (pixel_on)
            {
                int px = x + col;
                int py = y + row;
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    // 根据像素位置获取32x32渐变色
                    uint16_t color = getGradientColor32x32(px, py, gradientMode);
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 32x32竖向字符渐变色显示函数（向左旋转90度）
void drawChar32x32VerticalGradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode)
{
    // 向左旋转90度：原(col, row) -> 新(row, 31-col)
    for (int col = 0; col < 32; col++)
    {
        uint16_t column_data_low = font_data[col * 2];      // 低16位
        uint16_t column_data_high = font_data[col * 2 + 1]; // 高16位

        for (int row = 0; row < 32; row++)
        {
            bool pixel_on;
            if (row < 16)
            {
                pixel_on = (column_data_low & (0x8000 >> row)) != 0;
            }
            else
            {
                pixel_on = (column_data_high & (0x8000 >> (row - 16))) != 0;
            }

            if (pixel_on)
            {
                // 旋转坐标：原(col, row) -> 新(row, 31-col)
                int px = x + row;
                int py = y + (31 - col);
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    // 根据旋转后的像素位置获取32x32渐变色
                    uint16_t color = getGradientColor32x32(px, py, gradientMode);
                    drawTextPixel(px, py, color); // 🔧 使用坐标变换
                }
            }
        }
    }
}

// 32x32字符串渐变色显示函数
void drawString32x32Gradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t
        drawChar32x32Gradient(x + i * CHAR_SPACING_32, y, char_data, gradientMode);
    }
}

// 32x32竖向字符串渐变色显示函数
void drawString32x32VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t
        drawChar32x32VerticalGradient(x + i * CHAR_SPACING_32, y, char_data, gradientMode);
    }
}

// 获取32x32渐变色（严格对照16x16逻辑）
uint16_t getGradientColor32x32(int x, int y, uint8_t gradientMode)
{
    // 32x32全屏模式相当于16x16的上半屏，使用true
    return getGradientColor(x, y, true, gradientMode);
}

// 声明外部函数（在LEDController.cpp中实现）
extern uint16_t getCharacterColor(int charIndex, bool isUpper, int x, int y);

// ==================== 新增字符级颜色控制函数实现 ====================

// 16x16字符串字符级颜色显示
void drawString16x16WithCharColors(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, int startIndex)
{
    int pos_x = x;

    // 遍历每个字符的字库数据
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];

        // 获取该字符的颜色（使用统一颜色获取入口，传递全局字符索引）
        uint16_t charColor = getCharacterColor(startIndex + i, isUpper, pos_x, y);

        drawChar16x16(pos_x, y, char_data, charColor);
        pos_x += CHAR_SPACING_16; // 移动到下一个字符位置
    }
}

// 16x16竖向字符串字符级颜色显示
void drawString16x16VerticalWithCharColors(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, int startIndex)
{
    int pos_x = x;

    // 遍历每个字符的字库数据，向左旋转后从左到右排列
    for (int i = 0; i < char_count; i++)
    {
        // 每个字符占用16个uint16_t
        const uint16_t *char_data = &font_array[i * 16];

        // 获取该字符的颜色（使用统一颜色获取入口，传递全局字符索引）
        uint16_t charColor = getCharacterColor(startIndex + i, isUpper, pos_x, y);

        drawChar16x16Vertical(pos_x, y, char_data, charColor);
        pos_x += CHAR_SPACING_16; // 水平移动到下一个字符位置
    }
}

// 32x32字符串字符级颜色显示
void drawString32x32WithCharColors(int x, int y, const uint16_t *font_array, int char_count, int startIndex)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t

        // 32x32全屏模式使用上半屏标志（传递全局字符索引）
        uint16_t charColor = getCharacterColor(startIndex + i, true, x + i * CHAR_SPACING_32, y);

        drawChar32x32(x + i * CHAR_SPACING_32, y, char_data, charColor);
    }
}

// 32x32竖向字符串字符级颜色显示
void drawString32x32VerticalWithCharColors(int x, int y, const uint16_t *font_array, int char_count, int startIndex)
{
    for (int i = 0; i < char_count; i++)
    {
        const uint16_t *char_data = font_array + (i * 64); // 每个32x32字符64个uint16_t

        // 32x32全屏模式使用上半屏标志（传递全局字符索引）
        uint16_t charColor = getCharacterColor(startIndex + i, true, x + i * CHAR_SPACING_32, y);

        drawChar32x32Vertical(x + i * CHAR_SPACING_32, y, char_data, charColor);
    }
}