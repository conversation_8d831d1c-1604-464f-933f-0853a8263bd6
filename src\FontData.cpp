#include "FontData.h"

uint16_t upper_text[] = {};

uint16_t lower_text[] = {};

uint16_t full_text[] = {};


// ==================== 字库数据定义 ====================
// "人人人人人人" - 6个字符测试长文本分组显示
// uint16_t upper_text[] = {
//     // 第一个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第二个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第三个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第四个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第五个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第六个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000};

// // "人人人人人" - 5个字符测试长文本分组显示
// uint16_t lower_text[] = {
//     // 第一个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第二个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第三个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第四个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000,
//     // 第五个"人"字
//     0x0000, 0x0000, 0x0006, 0x0004,
//     0x0008, 0x0030, 0x00E0, 0x3F80,
//     0x2F00, 0x00E0, 0x0030, 0x0008,
//     0x0004, 0x0006, 0x0000, 0x0000};

// // 32x32向右箭头(→)图案 - 三个相同箭头测试
// uint16_t full_text[] = {
//     0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
//     0x0000, 0x0000, 0x0000, 0x0C00, 0x0030, 0x0C00, 0x0030, 0x1C00,
//     0x0070, 0x1800, 0x00E0, 0x1800, 0x01C0, 0x3800, 0x01D0, 0x3FFE,
//     0x0398, 0x7FFE, 0x071C, 0x7018, 0x1E1C, 0x7018, 0x3E0E, 0xF018,
//     0x3E07, 0xF018, 0x2603, 0xF018, 0x0603, 0xB018, 0x0607, 0x3018,
//     0x0607, 0x3018, 0x060E, 0x3018, 0x061C, 0x3018, 0x0638, 0x3018,
//     0x0670, 0x3018, 0x07E0, 0x3018, 0x07C0, 0x3018, 0x0780, 0x3FFE,
//     0x0600, 0x3FFE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,

//     0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
//     0x0000, 0x0000, 0x0000, 0x0C00, 0x0030, 0x0C00, 0x0030, 0x1C00,
//     0x0070, 0x1800, 0x00E0, 0x1800, 0x01C0, 0x3800, 0x01D0, 0x3FFE,
//     0x0398, 0x7FFE, 0x071C, 0x7018, 0x1E1C, 0x7018, 0x3E0E, 0xF018,
//     0x3E07, 0xF018, 0x2603, 0xF018, 0x0603, 0xB018, 0x0607, 0x3018,
//     0x0607, 0x3018, 0x060E, 0x3018, 0x061C, 0x3018, 0x0638, 0x3018,
//     0x0670, 0x3018, 0x07E0, 0x3018, 0x07C0, 0x3018, 0x0780, 0x3FFE,
//     0x0600, 0x3FFE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,

//     0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
//     0x0000, 0x0000, 0x0000, 0x0C00, 0x0030, 0x0C00, 0x0030, 0x1C00,
//     0x0070, 0x1800, 0x00E0, 0x1800, 0x01C0, 0x3800, 0x01D0, 0x3FFE,
//     0x0398, 0x7FFE, 0x071C, 0x7018, 0x1E1C, 0x7018, 0x3E0E, 0xF018,
//     0x3E07, 0xF018, 0x2603, 0xF018, 0x0603, 0xB018, 0x0607, 0x3018,
//     0x0607, 0x3018, 0x060E, 0x3018, 0x061C, 0x3018, 0x0638, 0x3018,
//     0x0670, 0x3018, 0x07E0, 0x3018, 0x07C0, 0x3018, 0x0780, 0x3FFE,
//     0x0600, 0x3FFE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000};

// ==================== 辅助函数实现 ====================
// 获取上半屏文本字符数
int getUpperTextCharCount()
{
    return sizeof(upper_text) / sizeof(uint16_t) / 16; // 16x16字体每字符16个uint16_t
}

// 获取下半屏文本字符数
int getLowerTextCharCount()
{
    return sizeof(lower_text) / sizeof(uint16_t) / 16; // 16x16字体每字符16个uint16_t
}

// 获取全屏文本字符数
int getFullTextCharCount()
{
    return sizeof(full_text) / sizeof(uint16_t) / 64; // 32x32字体每字符64个uint16_t
}